// Import third-party libraries
import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import helmet from "helmet";
import md5 from "md5";
import client from "prom-client";
import { v4 as uuidv4 } from "uuid";
// Import internal modules
import { RequestExtended } from "./interfaces/global";
import routes from "./routes";
import logger from "./utils/logger";
import apiLogger from "./middlewares/api-logger";
import "./config/db"; // Ensure database connection is established
import { initializeKafka, shutdownKafka } from "./kafka";
import {
  initializeIncrementalSyncCron,
  stopIncrementalSyncCron,
} from "./services/cron/instantSyncCron";

// Load environment variables from .env file
dotenv.config();

import { envConfig } from "./config/config";

const PORT = envConfig.server.port;

// Initialize Express application
const app = express();
app.disable("x-powered-by"); // Hide Express version

/**
 * ----------------------
 * Security Middlewares
 * ----------------------
 */
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        frameAncestors: ["'none'"],
        // Add other directives if needed
      },
    },
    referrerPolicy: {
      policy: "strict-origin-when-cross-origin",
    },
    strictTransportSecurity: {
      maxAge: 31536000,
      includeSubDomains: true,
    },
  })
);

// Set remaining security headers not handled by Helmet
app.use((req, res, next) => {
  res.setHeader("Permissions-Policy", "interest-cohort=()");
  res.setHeader(
    "Cache-Control",
    "no-store, no-cache, must-revalidate, max-age=0"
  );
  res.setHeader("Expires", "Wed, 11 Jan 1984 05:00:00 GMT");
  res.setHeader("Pragma", "no-cache");
  res.removeHeader("Server"); // Remove default Node server header
  next();
});

app.use(cors({ origin: "*" })); // Enable CORS for all origins
// app.use(rateLimiter); // Rate limiting middleware

/**
 * -------------------------
 * Prometheus Metrics Setup
 * -------------------------
 */
const collectDefaultMetrics = client.collectDefaultMetrics;
collectDefaultMetrics();

const httpRequestCounter = new client.Counter({
  name: "http_requests_total",
  help: "Total number of HTTP requests",
  labelNames: ["method", "route", "status"],
});

const httpRequestDurationHistogram = new client.Histogram({
  name: "http_request_duration_seconds",
  help: "Histogram of HTTP request durations in seconds",
  labelNames: ["method", "route", "status"],
  buckets: [0.1, 0.5, 1, 2, 5],
});

app.use((req: RequestExtended, res, next) => {
  const end = httpRequestDurationHistogram.startTimer();
  res.on("finish", () => {
    const routePath = req.route?.path || req.path;
    httpRequestCounter.inc({
      method: req.method,
      route: routePath,
      status: res.statusCode,
    });
    end({
      method: req.method,
      route: routePath,
      status: res.statusCode,
    });
  });
  next();
});

app.get("/metrics", async (req, res) => {
  res.set("Content-Type", client.register.contentType);
  res.send(await client.register.metrics());
});

/**
 * ---------------------
 * Logging & Tracing
 * ---------------------
 */
app.use((req: RequestExtended, res, next) => {
  req.messageId = req.header("messageId");
  req.id = md5(uuidv4());
  req.traceId = req.header("eg-request-id") || "-";

  req.logId = [`messageId[${req.messageId}]`].join(" ");

  req.log = (...args: any[]) => {
    const logMessage = [new Date().toISOString(), req.logId, ...args].join(" ");
    logger.info(logMessage);
  };

  req.error = (...args: any[]) => {
    const errorMessage = [new Date().toISOString(), req.logId, ...args].join(
      " "
    );
    logger.error(errorMessage);
  };

  logger.info(
    `Request received: ${req.method} ${req.originalUrl} - MessageID: ${req.messageId}`
  );

  res.on("finish", () => {
    logger.info(
      `Response sent with status: ${res.statusCode} - MessageID: ${req.messageId}`
    );
  });

  next();
});

/**
 * -----------------------------
 * Body Parsing
 * -----------------------------
 */
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

/**
 * -----------------------------
 * API Request Logging
 * -----------------------------
 */
app.use(apiLogger);

/**
 * -----------------------------
 * Health Check Endpoint
 * -----------------------------
 */
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: envConfig.server.nodeEnv,
    version: process.env.npm_package_version || "unknown",
  });
});

/**
 * -----------------------------
 * Routing
 * -----------------------------
 */
app.use("/api", routes);

/**
 * ---------------------
 * Start Server
 * ---------------------
 */
// Global variable to store cron task for graceful shutdown
let cronTask: any = null;

const server = app.listen(PORT, async () => {
  logger.info("Server is listening on port " + PORT);

  // Initialize Kafka producers and consumers
  try {
    await initializeKafka();
  } catch (error) {
    logger.error(`❌ Kafka initialization failed: ${error}`);
  }

  // Initialize incremental sync cron job
  try {
    cronTask = initializeIncrementalSyncCron();
  } catch (error) {
    logger.error(`❌ Cron job initialization failed: ${error}`);
  }
});

/**
 * ---------------------
 * Graceful Shutdown
 * ---------------------
 */
process.on("SIGTERM", async () => {
  logger.info("SIGTERM signal received. Shutting down gracefully...");

  // Stop cron job
  stopIncrementalSyncCron(cronTask);

  // Shutdown Kafka connections
  await shutdownKafka();

  // Close the server
  server.close(() => {
    logger.info("HTTP server closed");
    process.exit(0);
  });
});

process.on("SIGINT", async () => {
  logger.info("SIGINT signal received. Shutting down gracefully...");

  // Stop cron job
  stopIncrementalSyncCron(cronTask);

  // Shutdown Kafka connections
  await shutdownKafka();

  // Close the server
  server.close(() => {
    logger.info("HTTP server closed");
    process.exit(0);
  });
});
