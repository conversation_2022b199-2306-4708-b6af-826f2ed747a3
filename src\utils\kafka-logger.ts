/**
 * Utility for logging Kafka messages to the RequestLog table
 */
import { LogType } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import prisma from "../config/db";

/**
 * Log a Kafka message to the RequestLog table
 * @param topic - The Kafka topic
 * @param action - The action (PRODUCE or CONSUME)
 * @param message - The message content
 * @param connectionId - Optional connection ID if the message is related to a specific connection
 * @param status - Status code (200 for success, 4xx/5xx for errors)
 * @param errorMessage - Optional error message if there was an issue
 */
export const logKafkaMessage = async (
  topic: string,
  action: string,
  message: any,
  connectionId: string | null = null,
  status: number = 200,
  errorMessage?: string
) => {
  try {
    await prisma.requestLog.create({
      data: {
        sourceId: uuidv4(),
        connectionId,
        logType: LogType.KAFKA,
        endpointOrTopic: topic,
        methodOrAction: action,
        executionTime: new Date(),
        details: {
          message,
          error: status >= 400 ? errorMessage : null,
        },
        status,
        message:
          errorMessage ||
          `Kafka ${action.toLowerCase()} operation on topic ${topic}`,
      },
    });
  } catch (error) {
    console.error(`Error logging Kafka message: ${error}`);
  }
};

export default logKafkaMessage;
