/**
 * Message interfaces for communication between ZACT_APP and UNIFIED_BACKEND
 */

export enum MessageSource {
  ZACT_APP = "ZACT_APP",
  UNIFIED_BACKEND = "UNIFIED_BACKEND",
  ERP_SERVICE = "ERP_SERVICE",
}

export enum MessageDestination {
  ZACT_APP = "ZACT_APP",
  UNIFIED_BACKEND = "UNIFIED_BACKEND",
  ERP_SERVICE = "ERP_SERVICE",
}

export enum MessageType {
  REQUEST = "REQUEST",
  RESPONSE = "RESPONSE",
  EVENT = "EVENT",
}

export enum ERPSystem {
  QBO = "QBO",
  XERO = "XERO",
  NETSUITE = "NETSUITE",
}

export enum EntityType {
  ACCOUNT = "account",
  VENDOR = "vendor",
  CLASS = "class",
  BILL = "bill",
  PAYMENT = "payment",
  JOURNAL_ENTRY = "journal_entry",
  ALL = "all",
}

export enum OperationType {
  FETCH = "FETCH",
  CREATE = "CREATE",
  UPDATE = "UPDATE",
  DELETE = "DELETE",
  SYNC = "SYNC",
}

export interface EntityOperation {
  entityType?: EntityType;
  operation: OperationType;
}

export interface Pagination {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface Status {
  code: string;
  message: string;
}

export interface Metadata {
  processingTimeMs: number | null;
  errorType: string | null;
  errorDetails: string | null;
}

export interface SecurityContext {
  connectionId: string;
}

export interface BaseMessage {
  messageId: string;
  correlationId: string;
  timestamp: number;
  source: MessageSource;
  destination: MessageDestination;
  messageType: MessageType;
  erpSystem: ERPSystem;
  entityOperation: EntityOperation;
  filters: Record<string, any> | null;
  metadata?: Metadata;
  securityContext: SecurityContext;
}

export interface RequestMessage extends BaseMessage {
  messageType: MessageType.REQUEST;
  payload: Record<string, any> | null;
  status: null;
}

export interface ResponseMessage extends BaseMessage {
  messageType: MessageType.RESPONSE;
  payload: {
    data?: any;
    pagination?: Pagination;
    entityCounts?: Record<string, number>;
    message?: string;
  };
  status: Status;
}

export interface SyncCompletionMessage extends ResponseMessage {
  payload: {
    entityCounts: Record<string, number>;
    message: string;
  };
}

export interface EntityBatchMessage extends ResponseMessage {
  payload: {
    data: any[];
    entityType: EntityType;
    batchNumber: number;
    batchSize: number;
    totalBatches: number;
  };
}
