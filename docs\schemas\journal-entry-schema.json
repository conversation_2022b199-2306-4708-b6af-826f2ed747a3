{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Journal Entry En<PERSON><PERSON>", "description": "Schema for Journal Entry entities sent to Kafka in unified format", "type": "object", "required": ["id", "domain", "transactionDate", "lineItems"], "properties": {"id": {"type": "string", "description": "Unique identifier for the journal entry"}, "domain": {"type": "string", "description": "Platform domain identifier"}, "transactionDate": {"type": "string", "format": "date", "description": "MANDATORY - Required by ALL platforms. Date of the transaction (ISO date string)"}, "totalAmount": {"type": ["number", "null"], "description": "Total amount of the journal entry"}, "currencyCode": {"type": ["string", "null"], "description": "Currency code (e.g., USD, EUR)"}, "memo": {"type": ["string", "null"], "description": "MANDATORY - Required by ALL platforms (Sage Intacct requires memo as mandatory). Memo or description"}, "exchangeRate": {"type": ["number", "null"], "description": "Optional - Required by: Xero, NetSuite (not QBO, QBD). Exchange rate"}, "journalNumber": {"type": ["string", "null"], "description": "Optional - Required by: Xero, QBD, NetSuite (not QBO). Journal number"}, "postingStatus": {"type": ["string", "null"], "enum": ["POSTED", "UNPOSTED", null], "description": "Optional - Required by: Xero, NetSuite (not QBO, QBD). Posting status"}, "accountingPeriodId": {"type": ["string", "null"], "description": "Optional - Required by: NetSuite only. Accounting period ID"}, "inclusiveOfTax": {"type": ["boolean", "null"], "description": "Optional - Required by: NetSuite, Xero only. Whether amounts include tax"}, "companyId": {"type": ["string", "null"], "description": "Optional - Required by: NetSuite only. Company ID"}, "externalId": {"type": ["string", "null"], "description": "External ID provided by the accounting platform"}, "lastSyncedAt": {"type": ["string", "null"], "format": "date-time", "description": "Last sync timestamp"}, "platformUrl": {"type": ["string", "null"], "description": "Deep link URL to the platform"}, "lineItems": {"type": "array", "minItems": 2, "description": "MANDATORY - Array of line items (must have at least 2 for balanced entry)", "items": {"type": "object", "required": ["postingType", "totalAmount", "accountId"], "properties": {"id": {"type": ["string", "null"], "description": "Line item ID"}, "postingType": {"type": "string", "enum": ["Debit", "Credit"], "description": "MANDATORY - Required by ALL platforms. Posting type (Debit or Credit)"}, "totalAmount": {"type": "number", "minimum": 0, "description": "MANDATORY - Required by ALL platforms. Amount for this line item"}, "accountId": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Account ID for this line item"}, "detailType": {"type": "string", "description": "Detail type for the line item"}, "description": {"type": ["string", "null"], "description": "Description of the line item"}, "taxRateId": {"type": ["string", "null"], "description": "Optional - Required by: Xero, NetSuite (not QBO, QBD). Tax rate ID"}, "classId": {"type": ["string", "null"], "description": "Optional - Required by: QBO, NetSuite (not Xero, QBD). Class ID"}, "departmentId": {"type": ["string", "null"], "description": "Optional - Required by: NetSuite only. Department ID"}, "locationId": {"type": ["string", "null"], "description": "Optional - Required by: NetSuite only. Location ID"}}}}}, "examples": [{"id": "JE001", "domain": "qbo", "transactionDate": "2024-01-15", "totalAmount": 1000.0, "currencyCode": "USD", "memo": "Office rent payment adjustment", "exchangeRate": null, "journalNumber": "JE-2024-001", "postingStatus": "POSTED", "accountingPeriodId": null, "inclusiveOfTax": false, "companyId": null, "externalId": "JE001", "lastSyncedAt": "2024-01-15T10:30:00Z", "platformUrl": "https://qbo.intuit.com/app/journal/JE001", "lineItems": [{"id": "LINE001", "postingType": "Debit", "totalAmount": 1000.0, "accountId": "RENT_EXPENSE", "detailType": "JournalEntryLineDetail", "description": "Office rent expense", "taxRateId": null, "classId": "ADMIN", "departmentId": null, "locationId": null}, {"id": "LINE002", "postingType": "Credit", "totalAmount": 1000.0, "accountId": "CASH", "detailType": "JournalEntryLineDetail", "description": "Cash payment", "taxRateId": null, "classId": "ADMIN", "departmentId": null, "locationId": null}]}], "kafkaMessages": {"createRequestMessage": {"description": "Journal Entry creation request (inbound to unified backend)", "topic": "entity-create-request", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "ZACT_APP", "destination": "UNIFIED_BACKEND", "messageType": "REQUEST", "erpSystem": "QBO", "entityOperation": {"entityType": "journal_entry", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": [{"domain": "qbo", "transactionDate": "2024-01-15", "memo": "Office rent payment adjustment", "lineItems": [{"postingType": "Debit", "totalAmount": 1000.0, "accountId": "RENT_EXPENSE", "description": "Office rent expense"}, {"postingType": "Credit", "totalAmount": 1000.0, "accountId": "CASH", "description": "Cash payment"}]}], "status": null}}, "createResponseMessage": {"description": "Journal Entry creation response (outbound from unified backend)", "topic": "entity-create-response", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "journal_entry", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": {"data": {"id": "db-uuid-je-001", "externalId": "JE001", "domain": "qbo", "transactionDate": "2024-01-15", "memo": "Office rent payment adjustment"}}, "status": {"code": "200", "message": "Journal Entry created successfully"}}}}}