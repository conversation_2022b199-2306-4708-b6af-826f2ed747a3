# =============================================================================
# Docker Ignore File - Zact Unified Backend
# =============================================================================

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment files (copy manually if needed)
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md

# Test files
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
jest.config.js
jest.config.ts

# Development tools
.eslintrc*
.prettierrc*
.editorconfig
nodemon.json

# Database files (if any)
*.sqlite
*.sqlite3
*.db

# Backup files
*.bak
*.backup

# Prisma migrations (handled separately)
# prisma/migrations/

# Kubernetes
k8s/
kubernetes/
*.yaml
*.yml

# Terraform
*.tf
*.tfstate
*.tfvars

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript
tsconfig.json
tsconfig.*.json

# Linting
.eslintignore
.prettierignore

# IDE
*.iml
.project
.classpath
.settings/

# Misc
.bash_history
.cache
.config
.local
.ssh
