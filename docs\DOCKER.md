# 🐳 Docker Setup - Zact Unified Backend

This document provides comprehensive instructions for running the Zact Unified Backend using Docker.

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for containers
- At least 10GB disk space

## 🚀 Quick Start

### Production Deployment

```bash
# Clone the repository
git clone <repository-url>
cd zact-unified-backend

# Copy environment file and configure
cp .env.example .env
# Edit .env with your configuration

# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f app
```

### Development Setup

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View application logs
docker-compose -f docker-compose.dev.yml logs -f app-dev

# Access development tools:
# - Kafka UI: http://localhost:8080
# - phpMyAdmin: http://localhost:8081
```

## 📁 File Structure

```
├── Dockerfile              # Production Dockerfile
├── Dockerfile.dev          # Development Dockerfile
├── docker-compose.yml      # Production compose file
├── docker-compose.dev.yml  # Development compose file
├── .dockerignore           # Docker ignore file
└── docs/DOCKER.md          # This documentation
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Database
DATABASE_URL=mysql://zact_user:zact_password@mysql:3306/zact_unified

# Kafka
KAFKA_BROKERS=kafka:29092
KAFKA_CLIENT_ID=zact-unified-backend

# QBO Configuration
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret
QBO_ENVIRONMENT=sandbox

# API Gateway
API_GATEWAY_URL=http://localhost:8000

# Security
JWT_SECRET=your-super-secret-jwt-key

# Cron Jobs
INCREMENTAL_SYNC_CRON_ENABLED=true
INCREMENTAL_SYNC_CRON_SCHEDULE="0 */6 * * *"
```

### Port Configuration

| Service    | Production Port | Development Port | Description         |
| ---------- | --------------- | ---------------- | ------------------- |
| App        | 3000            | 3000             | Main application    |
| MySQL      | 3306            | 3307             | Database            |
| Kafka      | 9092            | 9093             | Message broker      |
| Redis      | 6379            | 6380             | Cache (optional)    |
| Kafka UI   | -               | 8080             | Kafka management    |
| phpMyAdmin | -               | 8081             | Database management |

## 🏗️ Build Process

### Production Build

The production Dockerfile uses multi-stage builds for optimization:

1. **Base Stage**: Sets up Node.js and system dependencies
2. **Dependencies Stage**: Installs production dependencies
3. **Builder Stage**: Compiles TypeScript and builds the application
4. **Production Stage**: Creates final optimized image

```bash
# Build production image
docker build -t zact-unified-backend:latest .

# Build with specific tag
docker build -t zact-unified-backend:v1.0.0 .
```

### Development Build

```bash
# Build development image
docker build -f Dockerfile.dev -t zact-unified-backend:dev .
```

## 🔍 Service Details

### Application Container

- **Base Image**: `node:20-alpine`
- **User**: Non-root user (nodejs:1001)
- **Security**: Uses dumb-init for proper signal handling
- **Health Check**: HTTP endpoint monitoring
- **Volumes**: Logs directory mounted

### MySQL Database

- **Image**: `mysql:8.0`
- **Authentication**: Native password plugin
- **Persistence**: Named volume for data
- **Health Check**: mysqladmin ping

### Kafka & Zookeeper

- **Images**: Confluent Platform 7.4.0
- **Configuration**: Single broker setup
- **Topics**: Auto-creation enabled
- **Retention**: 7 days (168 hours)

### Redis (Optional)

- **Image**: `redis:7-alpine`
- **Persistence**: Named volume
- **Configuration**: Default settings

## 🛠️ Development Tools

### Kafka UI

Access Kafka management interface at `http://localhost:8080`

Features:

- Topic management
- Message browsing
- Consumer group monitoring
- Broker information

### phpMyAdmin

Access database management at `http://localhost:8081`

Credentials:

- Server: mysql-dev
- Username: root
- Password: (from MYSQL_ROOT_PASSWORD)

## 📊 Monitoring & Logs

### View Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f app

# Last 100 lines
docker-compose logs --tail=100 app

# Development logs
docker-compose -f docker-compose.dev.yml logs -f app-dev
```

### Health Checks

```bash
# Check container health
docker-compose ps

# Detailed health status
docker inspect zact-unified-backend --format='{{.State.Health.Status}}'
```

### Resource Usage

```bash
# Container stats
docker stats

# Specific container
docker stats zact-unified-backend
```

## 🔧 Maintenance

### Database Operations

```bash
# Run Prisma migrations
docker-compose exec app npx prisma migrate deploy

# Generate Prisma client
docker-compose exec app npx prisma generate

# Database reset (development only)
docker-compose exec app npx prisma migrate reset
```

### Backup & Restore

```bash
# Backup database
docker-compose exec mysql mysqldump -u root -p zact_unified > backup.sql

# Restore database
docker-compose exec -T mysql mysql -u root -p zact_unified < backup.sql
```

### Updates

```bash
# Pull latest images
docker-compose pull

# Rebuild and restart
docker-compose up -d --build

# Remove old images
docker image prune -f
```

## 🚨 Troubleshooting

### Common Issues

1. **Port Conflicts**

   ```bash
   # Check port usage
   netstat -tulpn | grep :3000

   # Change ports in .env file
   PORT=3001
   ```

2. **Database Connection Issues**

   ```bash
   # Check MySQL logs
   docker-compose logs mysql

   # Test connection
   docker-compose exec app npm run test:db
   ```

3. **Kafka Connection Issues**

   ```bash
   # Check Kafka logs
   docker-compose logs kafka

   # Verify topics
   docker-compose exec kafka kafka-topics --list --bootstrap-server localhost:9092
   ```

### Performance Optimization

1. **Memory Limits**

   ```yaml
   # Add to docker-compose.yml
   services:
     app:
       deploy:
         resources:
           limits:
             memory: 1G
           reservations:
             memory: 512M
   ```

2. **Volume Optimization**
   ```bash
   # Use bind mounts for development
   volumes:
     - .:/app:cached
     - /app/node_modules
   ```

## 🔒 Security

### Production Security

- Non-root user execution
- Minimal base image (Alpine)
- No unnecessary packages
- Environment variable isolation
- Health check monitoring

### Network Security

```yaml
# Custom network configuration
networks:
  zact-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## 📈 Scaling

### Horizontal Scaling

```yaml
# Scale application instances
services:
  app:
    deploy:
      replicas: 3
```

### Load Balancing

```yaml
# Add nginx load balancer
nginx:
  image: nginx:alpine
  ports:
    - "80:80"
  volumes:
    - ./nginx.conf:/etc/nginx/nginx.conf
```

## 🎯 Best Practices

1. **Use specific image tags** instead of `latest`
2. **Implement proper health checks** for all services
3. **Use multi-stage builds** for smaller images
4. **Run as non-root user** for security
5. **Use .dockerignore** to exclude unnecessary files
6. **Implement proper logging** and monitoring
7. **Use named volumes** for data persistence
8. **Configure resource limits** appropriately
