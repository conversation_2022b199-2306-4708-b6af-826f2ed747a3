# Zact Unified Backend - Local Development Setup

This guide will help you set up the complete local development environment for the Zact Unified Backend.

## 🏗️ Architecture Overview

The local development environment consists of:

- **Backend Application**: Node.js/Express app running on port 8080
- **Local MySQL Database**: Your existing local MySQL instance
- **Kafka Infrastructure**: Containerized <PERSON><PERSON><PERSON>, Zookeeper, and Kafka UI
- **No Docker for Backend**: The main application runs directly on your machine

## 📋 Prerequisites

Before starting, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **npm** (comes with Node.js)
- **Docker Desktop** (for Kafka infrastructure)
- **MySQL Server** (running locally)

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

Run the complete setup script:

```powershell
npm run setup:full
```

This will:
- Install all dependencies
- Start Kafka infrastructure
- Set up the database
- Create Kafka topics
- Verify the setup

### Option 2: Manual Setup

If you prefer to set up components individually:

#### 1. Install Dependencies
```bash
npm install
```

#### 2. Start Kafka Infrastructure
```bash
npm run infra:start
```

#### 3. Setup Database
```bash
npm run db:setup
```

#### 4. Start Development Server
```bash
npm run dev
```

## 🔧 Infrastructure Management

### Kafka Infrastructure Commands

```bash
# Start infrastructure (Kafka, Zookeeper, Kafka UI)
npm run infra:start

# Stop infrastructure
npm run infra:stop

# Check status
npm run infra:status

# View logs
npm run infra:logs

# Clean up (removes all data)
npm run infra:clean
```

### Database Commands

```bash
# Setup database and run migrations
npm run db:setup

# Run Prisma migrations
npm run db:migrate

# Generate Prisma client
npm run db:generate

# Open Prisma Studio
npm run db:studio
```

## 🌐 Service URLs

Once everything is running, you can access:

- **Backend API**: http://localhost:8080
- **Kafka UI**: http://localhost:8090
- **Prisma Studio**: http://localhost:5555 (when running)

## 📊 Kafka Topics

The following topics are automatically created:

- `entity-create-request` - For entity creation requests
- `entity-create-response` - For entity creation responses
- `entity-batch-stream` - For batch processing
- `sync-completion-response` - For sync completion notifications

## 🗄️ Database Configuration

The application uses your local MySQL database as configured in `.env`:

```env
DATABASE_URL="mysql://root:12345678@localhost:3306/zact_staging"
```

Make sure your MySQL server is running and the credentials are correct.

## 🔍 Troubleshooting

### Common Issues

#### 1. Docker Not Running
```
❌ Docker is not running. Please start Docker Desktop first.
```
**Solution**: Start Docker Desktop and wait for it to be ready.

#### 2. MySQL Connection Failed
```
❌ Cannot connect to MySQL server
```
**Solutions**:
- Ensure MySQL server is running
- Check credentials in `.env` file
- Verify the database exists

#### 3. Port Already in Use
```
❌ Port 9092 is already in use
```
**Solution**: Stop any existing Kafka instances or change the port in `docker-compose.local.yml`.

#### 4. Kafka Not Ready
```
⚠️ Kafka: Not ready yet
```
**Solution**: Wait a few more seconds and check status again. Kafka takes time to start.

### Checking Service Health

```bash
# Check all services
npm run infra:status

# Check specific service logs
npm run infra:logs kafka
npm run infra:logs zookeeper
npm run infra:logs kafka-ui
```

### Reset Everything

If you need to start fresh:

```bash
# Stop and clean infrastructure
npm run infra:clean

# Remove node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Run full setup again
npm run setup:full
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- --testNamePattern="specific test"
```

## 📝 Development Workflow

1. **Start Infrastructure**: `npm run infra:start`
2. **Start Development Server**: `npm run dev`
3. **Make Changes**: Edit code in `src/` directory
4. **Test Changes**: Use Kafka UI to monitor messages
5. **Run Tests**: `npm test`

## 🔧 Configuration

### Environment Variables

Key environment variables for local development:

```env
# Server
PORT=8080
NODE_ENV=development

# Database (Local MySQL)
DATABASE_URL="mysql://root:12345678@localhost:3306/zact_staging"

# Kafka (Local Docker)
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=zact-unified-backend

# API Gateway
API_GATEWAY_URL=http://127.0.0.1:8000
```

### Kafka Configuration

The Kafka setup includes:
- **Broker**: localhost:9092
- **Zookeeper**: localhost:2181
- **UI**: localhost:8090
- **Auto-topic creation**: Enabled
- **Replication factor**: 1 (suitable for local development)

## 📚 Additional Resources

- [Kafka UI Documentation](https://github.com/provectus/kafka-ui)
- [Prisma Documentation](https://www.prisma.io/docs/)
- [KafkaJS Documentation](https://kafka.js.org/)

## 🆘 Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Review the logs: `npm run infra:logs`
3. Check service status: `npm run infra:status`
4. Try a clean restart: `npm run infra:clean` then `npm run setup:full`

---

**Happy coding! 🚀**
