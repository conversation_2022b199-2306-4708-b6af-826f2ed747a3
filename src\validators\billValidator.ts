import logger from "../utils/logger";

/**
 * Validate bill request payload
 */
export const validateBillRequest = (
  payload: any
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  logger.info("payload received for validation:", payload);
  
  if (!payload.vendorId) errors.push("vendorId is required");
  if (!payload.billNumber) errors.push("billNumber is required");
  if (!payload.billDate) errors.push("billDate is required");
  if (!payload.dueDate) errors.push("dueDate is required");
  if (!payload.totalAmount || payload.totalAmount <= 0) {
    errors.push("totalAmount is required and must be greater than 0");
  }
  // if (!payload.currency) errors.push("currency is required");

  // Validate date formats
  if (payload.billDate && isNaN(Date.parse(payload.billDate))) {
    errors.push("billDate must be a valid date");
  }
  if (payload.dueDate && isNaN(Date.parse(payload.dueDate))) {
    errors.push("dueDate must be a valid date");
  }

  if (
    !payload.lineItems ||
    !Array.isArray(payload.lineItems) ||
    payload.lineItems.length === 0
  ) {
    errors.push("lineItems is required and must be a non-empty array");
  }

  if (payload.lineItems && Array.isArray(payload.lineItems)) {
    payload.lineItems.forEach((item: any, index: number) => {
      if (!item.amount || item.amount <= 0) {
        errors.push(
          `lineItems[${index}].amount is required and must be greater than 0`
        );
      }
      if (!item.accountId) {
        errors.push(`lineItems[${index}].accountId is required`);
      }
    });
  }

  return { isValid: errors.length === 0, errors };
};
