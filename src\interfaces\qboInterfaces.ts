export interface IQboAccountResponse {
  Id: string;
  FullyQualifiedName: string;
  AccountType: string;
  AccountSubType?: string;
  Name: string;
  Classification?: string;
  ParentRef?: {
    value: string;
  };
  Active: boolean;
  CurrentBalance: number;
  SubAccount: boolean;
  CurrentBalanceWithSubAccounts: number;
  CurrencyRef?: {
    value: string;
    name?: string;
  };
  domain: string;
  createdDate?: string;
  updatedDate?: string;
}
