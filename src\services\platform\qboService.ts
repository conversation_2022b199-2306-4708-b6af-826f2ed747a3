import prisma from "../../config/db";
import { Prisma } from "@prisma/client";
import { mapQboAccountResponseToUnified } from "../../mappers/account_entity";
import { mapQboClassResponseToUnified } from "../../mappers/class_entity";
import { mapQboVendorResponseToUnified } from "../../mappers/vendor_entity";
import logger from "../../utils/logger";
import { SYNC_CONFIG } from "../../constants/syncConfig";

const BATCH_SIZE = SYNC_CONFIG.BATCH_SIZE;
const MAX_RETRIES = SYNC_CONFIG.MAX_RETRIES;

export interface ProcessingResult {
  processed: number;
  succeeded: number;
  failed: number;
}

const processWithRetry = async <T>(
  item: T,
  processor: (item: T) => Promise<any>,
  maxRetries = MAX_RETRIES
): Promise<any> => {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await processor(item);
    } catch (error) {
      lastError = error;
      if (attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  throw lastError;
};

/**
 * Generic batch processor for processing large datasets
 */
export const processBatch = async <T>(
  items: T[],
  processor: (item: T) => Promise<any>
): Promise<ProcessingResult> => {
  const result: ProcessingResult = { processed: 0, succeeded: 0, failed: 0 };

  for (let i = 0; i < items.length; i += BATCH_SIZE) {
    const batch = items.slice(i, i + BATCH_SIZE);

    const results = await Promise.allSettled(
      batch.map((item) => processWithRetry(item, processor))
    );

    results.forEach((promiseResult) => {
      result.processed++;
      if (promiseResult.status === "fulfilled") {
        result.succeeded++;
      } else {
        result.failed++;
        logger.error("Batch processing failed", {
          error: promiseResult.reason,
        });
      }
    });
  }

  return result;
};

const fetchQBODatafromDB = async (connectionId: string, entity: string) => {
  // Fetch entity data from the integration based on the connectionId and entity
  let data: any[] = [];
  switch (entity.toLowerCase()) {
    case "vendor":
      data = await prisma.vendor.findMany({
        where: { connectionId },
      });
      break;
    case "class":
      data = await prisma.class.findMany({
        where: { connectionId },
      });
      break;
    case "account":
      data = await prisma.account.findMany({
        where: { connectionId },
      });
      break;
    default:
      throw new Error(`Unsupported entity type: ${entity}`);
  }

  return {
    data: data,
    count: data.length,
    message: `Successfully fetched ${entity} data from QBO integration`,
  };
};

export const processAndSaveAccounts = async (
  connectionId: string,
  accountsData: any[]
): Promise<ProcessingResult> => {
  if (!accountsData?.length) {
    return { processed: 0, succeeded: 0, failed: 0 };
  }

  const processor = async (accountData: any) => {
    const unified = mapQboAccountResponseToUnified(accountData);

    return await prisma.account.upsert({
      where: {
        connectionId_externalAccountId: {
          connectionId,
          externalAccountId: unified.id,
        },
      },
      update: {
        name: unified.name,
        fullyQualifiedName: unified.fullyQualifiedName,
        accountType: unified.accountType,
        accountSubType: unified.accountSubType,
        category: unified.category,
        parentId: unified.parentId,
        active: unified.active,
        currentBalance: unified.currentBalance,
        subAccount: unified.subAccount,
        currentBalanceWithSubAccounts: unified.currentBalanceWithSubAccounts,
        currencyCode: unified.currencyCode,
        domain: unified.domain,
        createdAtPlatform: unified.createdAtPlatform,
        updatedAtPlatform: unified.updatedAtPlatform,
        lastSyncedAt: new Date(),
      },
      create: {
        connectionId,
        externalAccountId: unified.id,
        name: unified.name,
        fullyQualifiedName: unified.fullyQualifiedName,
        accountType: unified.accountType,
        accountSubType: unified.accountSubType,
        category: unified.category,
        parentId: unified.parentId,
        active: unified.active,
        currentBalance: unified.currentBalance,
        subAccount: unified.subAccount,
        currentBalanceWithSubAccounts: unified.currentBalanceWithSubAccounts,
        currencyCode: unified.currencyCode,
        domain: unified.domain,
        createdAtPlatform: unified.createdAtPlatform,
        updatedAtPlatform: unified.updatedAtPlatform,
        lastSyncedAt: new Date(),
      },
    });
  };

  return await processBatch(accountsData, processor);
};

/**
 * Enhanced version for instant sync with create/update detection
 */
export const processAndSaveAccountsWithDetection = async (
  connectionId: string,
  accountsData: any[]
): Promise<{ created: any[]; updated: any[]; summary: ProcessingResult }> => {
  if (!accountsData?.length) {
    return {
      created: [],
      updated: [],
      summary: { processed: 0, succeeded: 0, failed: 0 },
    };
  }

  const created: any[] = [];
  const updated: any[] = [];
  let processed = 0;
  let succeeded = 0;
  let failed = 0;

  for (const accountData of accountsData) {
    try {
      processed++;
      const unified = mapQboAccountResponseToUnified(accountData);

      // Check if record exists
      const existingAccount = await prisma.account.findUnique({
        where: {
          connectionId_externalAccountId: {
            connectionId,
            externalAccountId: unified.id,
          },
        },
      });

      const isUpdate = !!existingAccount;

      // Perform upsert
      const savedAccount = await prisma.account.upsert({
        where: {
          connectionId_externalAccountId: {
            connectionId,
            externalAccountId: unified.id,
          },
        },
        update: {
          name: unified.name,
          fullyQualifiedName: unified.fullyQualifiedName,
          accountType: unified.accountType,
          accountSubType: unified.accountSubType,
          category: unified.category,
          parentId: unified.parentId,
          active: unified.active,
          currentBalance: unified.currentBalance,
          subAccount: unified.subAccount,
          currentBalanceWithSubAccounts: unified.currentBalanceWithSubAccounts,
          currencyCode: unified.currencyCode,
          domain: unified.domain,
          createdAtPlatform: unified.createdAtPlatform,
          updatedAtPlatform: unified.updatedAtPlatform,
          lastSyncedAt: new Date(),
        },
        create: {
          connectionId,
          externalAccountId: unified.id,
          name: unified.name,
          fullyQualifiedName: unified.fullyQualifiedName,
          accountType: unified.accountType,
          accountSubType: unified.accountSubType,
          category: unified.category,
          parentId: unified.parentId,
          active: unified.active,
          currentBalance: unified.currentBalance,
          subAccount: unified.subAccount,
          currentBalanceWithSubAccounts: unified.currentBalanceWithSubAccounts,
          currencyCode: unified.currencyCode,
          domain: unified.domain,
          createdAtPlatform: unified.createdAtPlatform,
          updatedAtPlatform: unified.updatedAtPlatform,
          lastSyncedAt: new Date(),
        },
      });

      // Categorize the result
      if (isUpdate) {
        updated.push(savedAccount);
      } else {
        created.push(savedAccount);
      }

      succeeded++;
    } catch (error) {
      failed++;
      logger.error(`Failed to process account:`, error);
    }
  }

  return {
    created,
    updated,
    summary: { processed, succeeded, failed },
  };
};

export const processAndSaveClasses = async (
  connectionId: string,
  classesData: any[]
): Promise<ProcessingResult> => {
  if (!classesData?.length) {
    return { processed: 0, succeeded: 0, failed: 0 };
  }

  const processor = async (classData: any) => {
    const unified = mapQboClassResponseToUnified(classData);

    return await prisma.class.upsert({
      where: {
        connectionId_externalClassId: {
          connectionId,
          externalClassId: unified.id,
        },
      },
      update: {
        name: unified.name,
        hasChildren: unified.hasChildren,
        parentId: unified.parentId,
        isActive: unified.status,
        domain: unified.domain,
        createdAtPlatform: unified.createdAtPlatform,
        updatedAtPlatform: unified.updatedAtPlatform,
        lastSyncedAt: new Date(),
      },
      create: {
        connectionId,
        externalClassId: unified.id,
        name: unified.name,
        hasChildren: unified.hasChildren,
        parentId: unified.parentId,
        isActive: unified.status,
        domain: unified.domain,
        createdAtPlatform: unified.createdAtPlatform,
        updatedAtPlatform: unified.updatedAtPlatform,
        lastSyncedAt: new Date(),
      },
    });
  };

  return await processBatch(classesData, processor);
};

/**
 * Enhanced version for instant sync with create/update detection
 */
export const processAndSaveClassesWithDetection = async (
  connectionId: string,
  classesData: any[]
): Promise<{ created: any[]; updated: any[]; summary: ProcessingResult }> => {
  if (!classesData?.length) {
    return {
      created: [],
      updated: [],
      summary: { processed: 0, succeeded: 0, failed: 0 },
    };
  }

  const created: any[] = [];
  const updated: any[] = [];
  let processed = 0;
  let succeeded = 0;
  let failed = 0;

  for (const classData of classesData) {
    try {
      processed++;
      const unified = mapQboClassResponseToUnified(classData);

      // Check if record exists
      const existingClass = await prisma.class.findUnique({
        where: {
          connectionId_externalClassId: {
            connectionId,
            externalClassId: unified.id,
          },
        },
      });

      const isUpdate = !!existingClass;

      // Perform upsert
      const savedClass = await prisma.class.upsert({
        where: {
          connectionId_externalClassId: {
            connectionId,
            externalClassId: unified.id,
          },
        },
        update: {
          name: unified.name,
          hasChildren: unified.hasChildren,
          parentId: unified.parentId,
          isActive: unified.status,
          domain: unified.domain,
          createdAtPlatform: unified.createdAtPlatform,
          updatedAtPlatform: unified.updatedAtPlatform,
          lastSyncedAt: new Date(),
        },
        create: {
          connectionId,
          externalClassId: unified.id,
          name: unified.name,
          hasChildren: unified.hasChildren,
          parentId: unified.parentId,
          isActive: unified.status,
          domain: unified.domain,
          createdAtPlatform: unified.createdAtPlatform,
          updatedAtPlatform: unified.updatedAtPlatform,
          lastSyncedAt: new Date(),
        },
      });

      // Categorize the result
      if (isUpdate) {
        updated.push(savedClass);
      } else {
        created.push(savedClass);
      }

      succeeded++;
    } catch (error) {
      failed++;
      logger.error(`Failed to process class:`, error);
    }
  }

  return {
    created,
    updated,
    summary: { processed, succeeded, failed },
  };
};

export const processAndSaveVendors = async (
  connectionId: string,
  vendorsData: any[]
): Promise<ProcessingResult> => {
  if (!vendorsData?.length) {
    return { processed: 0, succeeded: 0, failed: 0 };
  }

  const processor = async (vendorData: any) => {
    const unified = mapQboVendorResponseToUnified(vendorData);

    return await prisma.vendor.upsert({
      where: {
        connectionId_externalVendorId: {
          connectionId,
          externalVendorId: unified.id,
        },
      },
      update: {
        vendorName: unified.vendorName,
        contactName: unified.contactName,
        email: unified.email,
        phone: unified.phone,
        website: unified.website,
        bankAccountNumber: unified.bankAccountNumber,
        addresses:
          unified.addresses === null ? Prisma.JsonNull : unified.addresses,
        isActive: unified.isActive,
        balance: unified.balance,
        taxNumber: unified.taxNumber,
        domain: unified.domain,
        createdAtPlatform: unified.createdAtPlatform,
        updatedAtPlatform: unified.updatedAtPlatform,
        lastSyncedAt: new Date(),
      },
      create: {
        connectionId,
        externalVendorId: unified.id,
        vendorName: unified.vendorName,
        contactName: unified.contactName,
        email: unified.email,
        phone: unified.phone,
        website: unified.website,
        bankAccountNumber: unified.bankAccountNumber,
        addresses:
          unified.addresses === null ? Prisma.JsonNull : unified.addresses,
        isActive: unified.isActive,
        balance: unified.balance,
        taxNumber: unified.taxNumber,
        domain: unified.domain,
        createdAtPlatform: unified.createdAtPlatform,
        updatedAtPlatform: unified.updatedAtPlatform,
        lastSyncedAt: new Date(),
      },
    });
  };

  return await processBatch(vendorsData, processor);
};

/**
 * Enhanced version for instant sync with create/update detection
 */
export const processAndSaveVendorsWithDetection = async (
  connectionId: string,
  vendorsData: any[]
): Promise<{ created: any[]; updated: any[]; summary: ProcessingResult }> => {
  if (!vendorsData?.length) {
    return {
      created: [],
      updated: [],
      summary: { processed: 0, succeeded: 0, failed: 0 },
    };
  }

  const created: any[] = [];
  const updated: any[] = [];
  let processed = 0;
  let succeeded = 0;
  let failed = 0;

  for (const vendorData of vendorsData) {
    try {
      processed++;
      const unified = mapQboVendorResponseToUnified(vendorData);

      // Check if record exists
      const existingVendor = await prisma.vendor.findUnique({
        where: {
          connectionId_externalVendorId: {
            connectionId,
            externalVendorId: unified.id,
          },
        },
      });

      const isUpdate = !!existingVendor;

      // Perform upsert
      const savedVendor = await prisma.vendor.upsert({
        where: {
          connectionId_externalVendorId: {
            connectionId,
            externalVendorId: unified.id,
          },
        },
        update: {
          vendorName: unified.vendorName,
          contactName: unified.contactName,
          email: unified.email,
          phone: unified.phone,
          website: unified.website,
          bankAccountNumber: unified.bankAccountNumber,
          addresses:
            unified.addresses === null ? Prisma.JsonNull : unified.addresses,
          isActive: unified.isActive,
          balance: unified.balance,
          taxNumber: unified.taxNumber,
          domain: unified.domain,
          createdAtPlatform: unified.createdAtPlatform,
          updatedAtPlatform: unified.updatedAtPlatform,
          lastSyncedAt: new Date(),
        },
        create: {
          connectionId,
          externalVendorId: unified.id,
          vendorName: unified.vendorName,
          contactName: unified.contactName,
          email: unified.email,
          phone: unified.phone,
          website: unified.website,
          bankAccountNumber: unified.bankAccountNumber,
          addresses:
            unified.addresses === null ? Prisma.JsonNull : unified.addresses,
          isActive: unified.isActive,
          balance: unified.balance,
          taxNumber: unified.taxNumber,
          domain: unified.domain,
          createdAtPlatform: unified.createdAtPlatform,
          updatedAtPlatform: unified.updatedAtPlatform,
          lastSyncedAt: new Date(),
        },
      });

      // Categorize the result
      if (isUpdate) {
        updated.push(savedVendor);
      } else {
        created.push(savedVendor);
      }

      succeeded++;
    } catch (error) {
      failed++;
      logger.error(`Failed to process vendor:`, error);
    }
  }

  return {
    created,
    updated,
    summary: { processed, succeeded, failed },
  };
};

/**
 * Clean up old synced data
 */
export const cleanup = async (
  connectionId: string,
  entityType: string,
  days = 90
) => {
  const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

  const entityMap = {
    account: () =>
      prisma.account.deleteMany({
        where: { connectionId, lastSyncedAt: { lt: cutoff } },
      }),
    vendor: () =>
      prisma.vendor.deleteMany({
        where: { connectionId, lastSyncedAt: { lt: cutoff } },
      }),
    class: () =>
      prisma.class.deleteMany({
        where: { connectionId, lastSyncedAt: { lt: cutoff } },
      }),
  };

  const handler = entityMap[entityType.toLowerCase() as keyof typeof entityMap];
  if (!handler) throw new Error(`Unsupported entity: ${entityType}`);

  const result = await handler();
  return { deletedCount: result.count };
};

export default fetchQBODatafromDB;
