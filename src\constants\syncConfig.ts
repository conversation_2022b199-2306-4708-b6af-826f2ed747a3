import { envConfig } from "../config/config";

/**
 * Sync configuration constants
 */
export const SYNC_CONFIG = {
  MAX_RETRIES: envConfig.sync.maxRetries,
  RETRY_DELAY: envConfig.sync.retryDelay,
  BATCH_SIZE: envConfig.sync.batchSize,
  MAX_BATCH_RETRIES: envConfig.sync.maxBatchRetries,
  DEFAULT_CLEANUP_DAYS: envConfig.sync.defaultCleanupDays,
  MAX_CONCURRENT_SYNCS: envConfig.sync.maxConcurrentSyncs,
} as const;

/**
 * Entity type mappings
 */
export const ENTITY_TYPE_MAPPING = {
  ACCOUNT: "ACCOUNT",
  VENDOR: "VENDOR",
  CLASS: "CLASS",
  BILL: "BILL",
  PAYMENT: "PAYMENT",
  JOURNAL_ENTRY: "JOURNAL_ENTRY",
} as const;

/**
 * Legacy CONFIG object for backward compatibility
 * @deprecated Use SYNC_CONFIG instead
 */
export const CONFIG = {
  MAX_RETRIES: SYNC_CONFIG.MAX_RETRIES,
  RETRY_DELAY: SYNC_CONFIG.RETRY_DELAY,
  SYNC_ERROR_TOPIC: "sync-completion-response",
} as const;
