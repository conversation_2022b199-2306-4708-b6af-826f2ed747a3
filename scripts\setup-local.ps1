# Zact Unified Backend - Local Development Setup Script
# This script sets up the local development environment with Kafka infrastructure

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("start", "stop", "restart", "status", "logs", "clean")]
    [string]$Action = "start",
    
    [Parameter(Mandatory=$false)]
    [string]$Service = "all"
)

$ErrorActionPreference = "Stop"

# Colors for output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }

# Check if Docker is running
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Check if docker-compose is available
function Test-DockerCompose {
    try {
        docker-compose --version | Out-Null
        return $true
    } catch {
        try {
            docker compose version | Out-Null
            return $true
        } catch {
            return $false
        }
    }
}

# Get docker-compose command
function Get-DockerComposeCommand {
    try {
        docker-compose --version | Out-Null
        return "docker-compose"
    } catch {
        return "docker compose"
    }
}

# Main functions
function Start-Infrastructure {
    Write-Info "🚀 Starting Zact Unified Backend Local Infrastructure..."
    
    if (-not (Test-DockerRunning)) {
        Write-Error "❌ Docker is not running. Please start Docker Desktop first."
        exit 1
    }
    
    if (-not (Test-DockerCompose)) {
        Write-Error "❌ Docker Compose is not available. Please install Docker Compose."
        exit 1
    }
    
    $dockerComposeCmd = Get-DockerComposeCommand
    
    Write-Info "📦 Starting infrastructure services..."
    & $dockerComposeCmd -f docker-compose.local.yml up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✅ Infrastructure services started successfully!"
        Write-Info "🔗 Services available at:"
        Write-Info "   • Kafka Broker: localhost:9092"
        Write-Info "   • Kafka UI: http://localhost:8090"
        Write-Info "   • Zookeeper: localhost:2181"
        Write-Info ""
        Write-Info "⏳ Waiting for services to be ready..."
        Start-Sleep -Seconds 10
        Show-Status
    } else {
        Write-Error "❌ Failed to start infrastructure services."
        exit 1
    }
}

function Stop-Infrastructure {
    Write-Info "🛑 Stopping Zact Unified Backend Local Infrastructure..."
    
    $dockerComposeCmd = Get-DockerComposeCommand
    & $dockerComposeCmd -f docker-compose.local.yml down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✅ Infrastructure services stopped successfully!"
    } else {
        Write-Error "❌ Failed to stop infrastructure services."
        exit 1
    }
}

function Restart-Infrastructure {
    Write-Info "🔄 Restarting Zact Unified Backend Local Infrastructure..."
    Stop-Infrastructure
    Start-Sleep -Seconds 3
    Start-Infrastructure
}

function Show-Status {
    Write-Info "📊 Infrastructure Status:"
    
    $dockerComposeCmd = Get-DockerComposeCommand
    & $dockerComposeCmd -f docker-compose.local.yml ps
    
    Write-Info ""
    Write-Info "🔍 Health Checks:"
    
    # Check Kafka
    try {
        $kafkaCheck = docker exec zact-kafka-local kafka-broker-api-versions --bootstrap-server localhost:9092 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Kafka: Healthy"
        } else {
            Write-Warning "⚠️  Kafka: Not ready yet"
        }
    } catch {
        Write-Warning "⚠️  Kafka: Not ready yet"
    }
    
    # Check Zookeeper
    try {
        $zkCheck = docker exec zact-zookeeper-local zkServer.sh status 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Zookeeper: Healthy"
        } else {
            Write-Warning "⚠️  Zookeeper: Not ready yet"
        }
    } catch {
        Write-Warning "⚠️  Zookeeper: Not ready yet"
    }
    
    # Check Kafka UI
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8090" -TimeoutSec 5 -UseBasicParsing 2>$null
        if ($response.StatusCode -eq 200) {
            Write-Success "✅ Kafka UI: Accessible at http://localhost:8090"
        } else {
            Write-Warning "⚠️  Kafka UI: Not accessible"
        }
    } catch {
        Write-Warning "⚠️  Kafka UI: Not accessible yet"
    }
}

function Show-Logs {
    Write-Info "📋 Showing logs for infrastructure services..."
    
    $dockerComposeCmd = Get-DockerComposeCommand
    
    if ($Service -eq "all") {
        & $dockerComposeCmd -f docker-compose.local.yml logs -f
    } else {
        & $dockerComposeCmd -f docker-compose.local.yml logs -f $Service
    }
}

function Clean-Infrastructure {
    Write-Warning "🧹 This will remove all containers, volumes, and data. Are you sure? (y/N)"
    $confirmation = Read-Host
    
    if ($confirmation -eq "y" -or $confirmation -eq "Y") {
        Write-Info "🧹 Cleaning up infrastructure..."
        
        $dockerComposeCmd = Get-DockerComposeCommand
        & $dockerComposeCmd -f docker-compose.local.yml down -v --remove-orphans
        
        Write-Success "✅ Infrastructure cleaned successfully!"
    } else {
        Write-Info "❌ Cleanup cancelled."
    }
}

# Main execution
Write-Info "🏗️  Zact Unified Backend - Local Development Setup"
Write-Info "=================================================="

switch ($Action) {
    "start" { Start-Infrastructure }
    "stop" { Stop-Infrastructure }
    "restart" { Restart-Infrastructure }
    "status" { Show-Status }
    "logs" { Show-Logs }
    "clean" { Clean-Infrastructure }
    default { 
        Write-Error "❌ Invalid action: $Action"
        Write-Info "Valid actions: start, stop, restart, status, logs, clean"
        exit 1
    }
}
