/**
 * Validate journal entry request payload
 */
export const validateJournalEntryRequest = (
  payload: any
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!payload.transactionDate) errors.push("transactionDate is required");
  if (
    !payload.lineItems ||
    !Array.isArray(payload.lineItems) ||
    payload.lineItems.length === 0
  ) {
    errors.push("lineItems is required and must be a non-empty array");
  }

  if (payload.lineItems && Array.isArray(payload.lineItems)) {
    payload.lineItems.forEach((item: any, index: number) => {
      if (
        !item.postingType ||
        !["Debit", "Credit"].includes(item.postingType)
      ) {
        errors.push(
          `lineItems[${index}].postingType is required and must be 'Debit' or 'Credit'`
        );
      }
      if (!item.totalAmount || item.totalAmount <= 0) {
        errors.push(
          `lineItems[${index}].totalAmount is required and must be greater than 0`
        );
      }
      if (!item.accountId) {
        errors.push(`lineItems[${index}].accountId is required`);
      }
      if (!item.description && typeof item.description !== "string") {
        errors.push(`lineItems[${index}].description is required`);
      }
    });
  }

  return { isValid: errors.length === 0, errors };
};
