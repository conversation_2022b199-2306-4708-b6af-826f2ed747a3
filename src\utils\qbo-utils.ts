import axios from "axios";
import { SyncOperationType } from "@prisma/client";
import ApiException from "./api-exception";
import logger from "./logger";
import { ErrorCode, HttpStatus } from "./response";
import { executeFullSyncWithMessaging } from "../services/kafka-message/index";
import { createAuthData } from "./auth-utils";

/**
 * Helper function to start a background sync process for QBO
 * @param connectionId - The connection ID to sync
 * @param accessToken - The access token for QBO API authentication
 * @param realmId - The QBO realm ID
 * @param syncType - The type of sync operation to perform
 * @param context - Optional context information for logging
 */
export function startQboBackgroundSync(
  connectionId: string,
  accessToken: string,
  realmId: string,
  syncType: SyncOperationType,
  context: string = ""
): void {
  const contextPrefix = context ? `${context} ` : "";

  // Start the sync process in the background
  (async () => {
    try {
      logger.info(
        `Starting ${syncType} sync for ${contextPrefix}QBO connection ${connectionId}`
      );

      await executeFullSyncWithMessaging(
        "qbo",
        connectionId,
        accessToken,
        realmId,
        syncType
      );

      logger.info(
        `${syncType} sync completed for ${contextPrefix}QBO connection ${connectionId}`
      );
    } catch (syncError) {
      logger.error(
        `Error during ${syncType} sync for ${contextPrefix}QBO connection ${connectionId}`,
        {
          error: syncError,
          connectionId,
          syncType,
        }
      );
    }
  })();
}

/**
 * Fetch company information from QBO
 * @param accessToken - QBO access token
 * @param realmId - QBO realm ID
 * @param baseUrl - QBO API base URL
 * @returns Company name
 */
export async function fetchQboCompanyInfo(
  accessToken: string,
  realmId: string,
  baseUrl: string
): Promise<string> {
  const response = await axios.get(
    `${baseUrl}/${realmId}/companyinfo/${realmId}`,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
    }
  );
  return response.data.CompanyInfo.CompanyName;
}

/**
 * Handle reconnection of an inactive QBO integration
 * @param integrationId - The integration ID
 * @param orgId - The organization ID
 * @param accessToken - The QBO access token
 * @param refreshToken - The QBO refresh token
 * @param expiresIn - Token expiration time in seconds
 * @param realmId - The QBO realm ID
 * @param companyName - The company name
 * @returns Response object with connection details
 */
export async function handleQboInactiveIntegrationReconnection(
  integrationId: string,
  orgId: string,
  accessToken: string,
  refreshToken: string,
  expiresIn: number,
  realmId: string,
  companyName: string
) {
  // Import prisma and ConnectionStatus here to avoid circular dependencies
  const { default: prisma } = require("../config/db");
  const { ConnectionStatus, SyncOperationType } = require("@prisma/client");

  // Update the existing inactive integration
  const authData = createAuthData(accessToken, refreshToken, expiresIn);
  const updatedIntegration = await prisma.accountingPlatformIntegration.update({
    where: {
      id: integrationId,
    },
    data: {
      zactCompanyId: orgId,
      connectionStatus: ConnectionStatus.ACTIVE,
      lastConnectedAt: new Date(),
      authentication: authData,
      companyName,
    },
  });

  // Start an instant sync for the reconnected integration
  startQboBackgroundSync(
    updatedIntegration.id,
    accessToken,
    realmId,
    SyncOperationType.INCREMENTAL_SYNC,
    "reconnected"
  );

  // Return success response
  return {
    message:
      "Successfully reconnected to QBO and updated organization association. Instant sync started in background.",
    data: {
      connectionId: integrationId,
      realmId,
      erpSystem: "qbo",
    },
  };
}
