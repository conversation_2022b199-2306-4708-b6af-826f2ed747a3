#!/bin/bash

# Zact Unified Backend - Local Development Setup Script
# This script sets up the local development environment with Kafka infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${CYAN}$1${NC}"
}

log_success() {
    echo -e "${GREEN}$1${NC}"
}

log_warning() {
    echo -e "${YELLOW}$1${NC}"
}

log_error() {
    echo -e "${RED}$1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "❌ Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Check if docker-compose is available
check_docker_compose() {
    if command -v docker-compose >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker-compose"
    elif docker compose version >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker compose"
    else
        log_error "❌ Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
}

# Start infrastructure
start_infrastructure() {
    log_info "🚀 Starting Zact Unified Backend Local Infrastructure..."
    
    check_docker
    check_docker_compose
    
    log_info "📦 Starting infrastructure services..."
    $DOCKER_COMPOSE_CMD -f docker-compose.local.yml up -d
    
    if [ $? -eq 0 ]; then
        log_success "✅ Infrastructure services started successfully!"
        log_info "🔗 Services available at:"
        log_info "   • Kafka Broker: localhost:9092"
        log_info "   • Kafka UI: http://localhost:8090"
        log_info "   • Zookeeper: localhost:2181"
        log_info ""
        log_info "⏳ Waiting for services to be ready..."
        sleep 10
        show_status
    else
        log_error "❌ Failed to start infrastructure services."
        exit 1
    fi
}

# Stop infrastructure
stop_infrastructure() {
    log_info "🛑 Stopping Zact Unified Backend Local Infrastructure..."
    
    check_docker_compose
    $DOCKER_COMPOSE_CMD -f docker-compose.local.yml down
    
    if [ $? -eq 0 ]; then
        log_success "✅ Infrastructure services stopped successfully!"
    else
        log_error "❌ Failed to stop infrastructure services."
        exit 1
    fi
}

# Restart infrastructure
restart_infrastructure() {
    log_info "🔄 Restarting Zact Unified Backend Local Infrastructure..."
    stop_infrastructure
    sleep 3
    start_infrastructure
}

# Show status
show_status() {
    log_info "📊 Infrastructure Status:"
    
    check_docker_compose
    $DOCKER_COMPOSE_CMD -f docker-compose.local.yml ps
    
    echo ""
    log_info "🔍 Health Checks:"
    
    # Check Kafka
    if docker exec zact-kafka-local kafka-broker-api-versions --bootstrap-server localhost:9092 >/dev/null 2>&1; then
        log_success "✅ Kafka: Healthy"
    else
        log_warning "⚠️  Kafka: Not ready yet"
    fi
    
    # Check Zookeeper
    if docker exec zact-zookeeper-local zkServer.sh status >/dev/null 2>&1; then
        log_success "✅ Zookeeper: Healthy"
    else
        log_warning "⚠️  Zookeeper: Not ready yet"
    fi
    
    # Check Kafka UI
    if curl -s http://localhost:8090 >/dev/null 2>&1; then
        log_success "✅ Kafka UI: Accessible at http://localhost:8090"
    else
        log_warning "⚠️  Kafka UI: Not accessible yet"
    fi
}

# Show logs
show_logs() {
    log_info "📋 Showing logs for infrastructure services..."
    
    check_docker_compose
    
    if [ "$2" = "all" ] || [ -z "$2" ]; then
        $DOCKER_COMPOSE_CMD -f docker-compose.local.yml logs -f
    else
        $DOCKER_COMPOSE_CMD -f docker-compose.local.yml logs -f "$2"
    fi
}

# Clean infrastructure
clean_infrastructure() {
    log_warning "🧹 This will remove all containers, volumes, and data. Are you sure? (y/N)"
    read -r confirmation
    
    if [ "$confirmation" = "y" ] || [ "$confirmation" = "Y" ]; then
        log_info "🧹 Cleaning up infrastructure..."
        
        check_docker_compose
        $DOCKER_COMPOSE_CMD -f docker-compose.local.yml down -v --remove-orphans
        
        log_success "✅ Infrastructure cleaned successfully!"
    else
        log_info "❌ Cleanup cancelled."
    fi
}

# Show help
show_help() {
    echo "Zact Unified Backend - Local Development Setup"
    echo "=============================================="
    echo ""
    echo "Usage: $0 [ACTION] [SERVICE]"
    echo ""
    echo "Actions:"
    echo "  start     Start infrastructure services (default)"
    echo "  stop      Stop infrastructure services"
    echo "  restart   Restart infrastructure services"
    echo "  status    Show status of services"
    echo "  logs      Show logs (optionally for specific service)"
    echo "  clean     Remove all containers and volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Services (for logs):"
    echo "  all       All services (default)"
    echo "  kafka     Kafka broker only"
    echo "  zookeeper Zookeeper only"
    echo "  kafka-ui  Kafka UI only"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs kafka"
    echo "  $0 status"
}

# Main execution
ACTION=${1:-start}
SERVICE=${2:-all}

log_info "🏗️  Zact Unified Backend - Local Development Setup"
log_info "=================================================="

case $ACTION in
    start)
        start_infrastructure
        ;;
    stop)
        stop_infrastructure
        ;;
    restart)
        restart_infrastructure
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$@"
        ;;
    clean)
        clean_infrastructure
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "❌ Invalid action: $ACTION"
        echo ""
        show_help
        exit 1
        ;;
esac
