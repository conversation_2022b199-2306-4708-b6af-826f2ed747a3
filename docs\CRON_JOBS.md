# Incremental Sync Cron Job Documentation

## Overview

The Incremental Sync Cron Job automatically performs incremental synchronization for all active QBO connections at scheduled intervals. This ensures that data stays up-to-date without manual intervention.

## Features

- **Automated Scheduling**: Configurable cron schedule via environment variables
- **Batch Processing**: Handles large datasets efficiently with configurable batch sizes
- **Concurrent Processing**: Processes multiple connections simultaneously with rate limiting
- **Retry Logic**: Automatic retry mechanism for failed sync operations
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Graceful Shutdown**: Proper cleanup during application shutdown
- **Manual Triggering**: API endpoint for manual testing and triggering

## Configuration

All cron job settings are configured via environment variables:

### Core Settings

```bash
# Enable/Disable the cron job
INCREMENTAL_SYNC_CRON_ENABLED=true

# Cron schedule (standard cron syntax)
INCREMENTAL_SYNC_CRON_SCHEDULE="0 */6 * * *"

# Entity types to sync (comma-separated)
INCREMENTAL_SYNC_CRON_ENTITIES="ACCOUNT,VENDOR,CLA<PERSON>"
```

### Performance Settings

```bash
# Batch size for large datasets
INCREMENTAL_SYNC_CRON_BATCH_SIZE=1000

# Maximum concurrent connections
INCREMENTAL_SYNC_CRON_MAX_CONCURRENT=3

# Retry configuration
INCREMENTAL_SYNC_CRON_RETRY_ATTEMPTS=3
INCREMENTAL_SYNC_CRON_RETRY_DELAY=5000

# Timezone for cron execution
CRON_TIMEZONE="UTC"
```

## Cron Schedule Examples

| Schedule           | Description                   |
| ------------------ | ----------------------------- |
| `"*/15 * * * *"`   | Every 15 minutes              |
| `"0 * * * *"`      | Every hour                    |
| `"0 */4 * * *"`    | Every 4 hours                 |
| `"0 */6 * * *"`    | Every 6 hours (default)       |
| `"0 2 * * *"`      | Daily at 2 AM                 |
| `"0 6,18 * * *"`   | Twice daily (6 AM and 6 PM)   |
| `"0 0 * * 0"`      | Weekly on Sunday at midnight  |
| `"0 9-17 * * 1-5"` | Business hours, weekdays only |

## How It Works

1. **Initialization**: Cron job starts when the application boots up
2. **Schedule Execution**: Runs according to the configured schedule
3. **Connection Discovery**: Finds all active QBO connections
4. **Batch Processing**: Processes connections in configurable batches
5. **Entity Sync**: Syncs each configured entity type for each connection
6. **Retry Logic**: Retries failed operations with exponential backoff
7. **Logging**: Comprehensive logging of all operations and results

## Processing Flow

```
Cron Job Trigger
    ↓
Get Active Connections
    ↓
For Each Entity Type:
    ↓
Process Connections in Batches
    ↓
For Each Connection:
    ↓
Get Valid QBO Token
    ↓
Perform Incremental Sync
    ↓
Handle Success/Failure
    ↓
Retry if Failed (up to max attempts)
    ↓
Log Results
    ↓
Continue to Next Connection
    ↓
Generate Summary Report
```

## API Endpoints

### Manual Trigger

Manually trigger the cron job for testing:

```bash
POST /api/sync/cron/trigger
```

**Response:**

```json
{
  "message": "Incremental sync cron job triggered successfully",
  "status": "running",
  "note": "Check server logs for detailed progress and results"
}
```

## Monitoring and Logging

The cron job provides comprehensive logging at different levels:

### Info Level Logs

- Cron job start/completion
- Connection discovery
- Batch processing progress
- Success summaries

### Error Level Logs

- Failed sync operations
- Token refresh failures
- Connection issues
- Retry attempts

### Sample Log Output

```
[INFO] Starting incremental sync cron job
[INFO] Found 25 active connections for cron sync
[INFO] Starting cron sync for entity type: VENDOR
[INFO] Processing batch 1 for VENDOR
[INFO] Cron sync successful for company-123 - VENDOR
[ERROR] Cron sync failed for company-456 - VENDOR (attempt 1)
[INFO] Retrying cron sync for company-456 - VENDOR in 5000ms
[INFO] Incremental sync cron job completed
```

## Performance Considerations

### Recommended Settings by Environment

**Production (High Volume)**

```bash
INCREMENTAL_SYNC_CRON_SCHEDULE="0 */4 * * *"
INCREMENTAL_SYNC_CRON_BATCH_SIZE=2000
INCREMENTAL_SYNC_CRON_MAX_CONCURRENT=5
INCREMENTAL_SYNC_CRON_RETRY_ATTEMPTS=5
```

**Development/Testing**

```bash
INCREMENTAL_SYNC_CRON_SCHEDULE="0 */12 * * *"
INCREMENTAL_SYNC_CRON_BATCH_SIZE=500
INCREMENTAL_SYNC_CRON_MAX_CONCURRENT=2
INCREMENTAL_SYNC_CRON_RETRY_ATTEMPTS=2
```

**Debug/Testing**

```bash
INCREMENTAL_SYNC_CRON_SCHEDULE="*/5 * * * *"
INCREMENTAL_SYNC_CRON_BATCH_SIZE=100
INCREMENTAL_SYNC_CRON_MAX_CONCURRENT=1
INCREMENTAL_SYNC_CRON_RETRY_ATTEMPTS=1
```

## Troubleshooting

### Common Issues

1. **Cron Job Not Running**

   - Check `INCREMENTAL_SYNC_CRON_ENABLED=true`
   - Verify cron schedule syntax
   - Check application logs for initialization errors

2. **High Memory Usage**

   - Reduce `INCREMENTAL_SYNC_CRON_BATCH_SIZE`
   - Reduce `INCREMENTAL_SYNC_CRON_MAX_CONCURRENT`
   - Increase cron interval

3. **Rate Limiting Issues**

   - Reduce `INCREMENTAL_SYNC_CRON_MAX_CONCURRENT`
   - Increase `INCREMENTAL_SYNC_CRON_RETRY_DELAY`
   - Increase cron interval

4. **Token Refresh Failures**
   - Check QBO credentials
   - Verify connection status
   - Check token expiration

### Debug Mode

For debugging, use frequent execution:

```bash
INCREMENTAL_SYNC_CRON_ENABLED=true
INCREMENTAL_SYNC_CRON_SCHEDULE="*/2 * * * *"  # Every 2 minutes
INCREMENTAL_SYNC_CRON_ENTITIES="VENDOR"       # Single entity for testing
INCREMENTAL_SYNC_CRON_MAX_CONCURRENT=1        # Single connection
```

## Security Considerations

- Cron job uses existing QBO token validation
- No additional authentication required
- Respects connection status (only active connections)
- Proper error handling prevents sensitive data exposure
- Comprehensive logging for audit trails

## Graceful Shutdown

The cron job properly handles application shutdown:

1. Receives shutdown signal (SIGTERM/SIGINT)
2. Stops accepting new cron executions
3. Allows current operations to complete
4. Cleans up resources
5. Exits gracefully

This ensures no data corruption or incomplete sync operations during deployment or restart scenarios.
