import axios from "axios";
import { ConnectionStatus } from "@prisma/client";
import prisma from "../config/db";
import ApiException from "./api-exception";
import logger from "./logger";
import { ErrorCode, HttpStatus } from "./response";

/**
 * Helper function to create authentication data object with expiration
 * @param accessToken - The access token
 * @param refreshToken - The refresh token
 * @param expiresIn - Token expiration time in seconds
 * @returns Authentication data object with tokens and expiration timestamps
 */
export function createAuthData(
  accessToken: string,
  refreshToken: string,
  expiresIn: number
) {
  const expiresAt = new Date(Date.now() + expiresIn * 1000);
  return {
    accessToken,
    refreshToken,
    expiresAt: expiresAt.toISOString(),
    lastRefreshedAt: new Date().toISOString(),
  };
}

/**
 * Generic function to exchange authorization code for access token
 * @param code - Authorization code from OAuth provider
 * @param clientId - OAuth client ID
 * @param clientSecret - OAuth client secret
 * @param redirectUri - <PERSON><PERSON><PERSON> redirect URI
 * @param tokenUrl - OAuth token endpoint URL
 * @returns Access token information
 */
export async function exchangeCodeForToken(
  code: string,
  clientId: string,
  clientSecret: string,
  redirectUri: string,
  tokenUrl: string
): Promise<{
  access_token: string;
  refresh_token: string;
  expires_in: number;
}> {
  const params = new URLSearchParams({
    grant_type: "authorization_code",
    code,
    redirect_uri: redirectUri,
  });

  const response = await axios.post(tokenUrl, params, {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Basic ${Buffer.from(
        `${clientId}:${clientSecret}`
      ).toString("base64")}`,
    },
  });

  if (!response.data.access_token || !response.data.refresh_token) {
    throw new Error("Missing token data in response");
  }

  return {
    access_token: response.data.access_token,
    refresh_token: response.data.refresh_token,
    expires_in: response.data.expires_in,
  };
}

/**
 * Check for existing integrations
 * @param orgId - Organization ID
 * @param externalCompanyId - External company ID
 * @param platformType - Accounting platform type
 * @returns Existing integrations
 */
export async function checkExistingIntegrations(
  orgId: string,
  externalCompanyId: string,
  platformType: string
) {
  // Check if there's an existing integration with the same external company ID but different zactCompanyId
  const existingIntegrationWithSameExternalId =
    await prisma.accountingPlatformIntegration.findFirst({
      where: {
        externalCompanyId,
        accountingPlatformType: platformType,
        zactCompanyId: {
          not: orgId, // Different zact company ID
        },
      },
    });

  // Check if the current zact company already has an integration
  const existingIntegration =
    await prisma.accountingPlatformIntegration.findUnique({
      where: {
        zactCompanyId: orgId,
      },
    });

  return { existingIntegration, existingIntegrationWithSameExternalId };
}

/**
 * Create or update an integration
 * @param orgId - Organization ID
 * @param externalCompanyId - External company ID
 * @param companyName - Company name
 * @param authData - Authentication data
 * @param platformType - Accounting platform type
 * @returns Created or updated integration
 */
export async function upsertIntegration(
  orgId: string,
  externalCompanyId: string,
  companyName: string,
  authData: any,
  platformType: string
) {
  const now = new Date();
  const integrationData = {
    accountingPlatformType: platformType,
    externalCompanyId,
    companyName,
    connectionStatus: ConnectionStatus.ACTIVE,
    lastConnectedAt: now,
    authentication: authData,
  };

  return await prisma.accountingPlatformIntegration.upsert({
    where: {
      zactCompanyId: orgId,
    },
    update: integrationData,
    create: {
      zactCompanyId: orgId,
      ...integrationData,
    },
  });
}
