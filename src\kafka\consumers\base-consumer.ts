import { Consumer, EachMessagePayload } from "kafkajs";
import { v4 as uuidv4 } from "uuid";
import logger from "../../utils/logger";
import { createConsumer } from "../config/kafka-config";
import prisma from "../../config/db";
import { LogType } from "@prisma/client";
import baseProducer from "../producers/base-producer";

export interface MessageHandler {
  (payload: any, messageId: string): Promise<any>;
}

class BaseConsumer {
  private consumer: Consumer | null = null;
  private isConnected: boolean = false;
  private handlers: Map<string, MessageHandler> = new Map();
  private responseTopics: Map<string, string> = new Map();

  constructor() {
    // Initialization will be called explicitly
  }

  async initialize() {
    if (this.isConnected && this.consumer) {
      return; // Already initialized
    }

    try {
      this.consumer = await createConsumer();
      this.isConnected = true;
      // Logging moved to main kafka init
    } catch (error) {
      logger.error(`❌ Consumer initialization failed: ${error}`);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Subscribe to a Kafka topic
   * @param topic The Kafka topic to subscribe to
   * @param handler The message handler function
   * @param responseTopicName Optional topic to send response messages to
   */
  async subscribe(
    topic: string,
    handler: MessageHandler,
    responseTopicName?: string
  ): Promise<void> {
    if (!this.isConnected || !this.consumer) {
      throw new Error("Consumer not initialized. Call initialize() first.");
    }

    try {
      // Store the handler and response topic
      this.handlers.set(topic, handler);
      if (responseTopicName) {
        this.responseTopics.set(topic, responseTopicName);
      }

      // Subscribe to the topic
      await this.consumer.subscribe({ topic, fromBeginning: false });

      // Set up the message processor if not already running
      if (this.handlers.size === 1) {
        await this.startMessageProcessor();
      }

      // Log consumer subscription
      logger.info(`📥 Consumer listening on topic: ${topic}`);
    } catch (error) {
      logger.error(`Failed to subscribe to topic ${topic}: ${error}`);
      throw error;
    }
  }

  /**
   * Start processing messages from subscribed topics
   */
  private async startMessageProcessor(): Promise<void> {
    if (!this.consumer) return;

    try {
      await this.consumer.run({
        eachMessage: async (payload: EachMessagePayload) => {
          const { topic, partition, message } = payload;
          const sourceId = message.key?.toString() || uuidv4();

          // Extract message value
          let messageValue: any = {};
          try {
            messageValue = message.value
              ? JSON.parse(message.value.toString())
              : {};
          } catch (parseError) {
            logger.error(
              `Failed to parse message from topic ${topic}: ${parseError}`
            );
            await this.logMessageToDb(
              sourceId,
              messageValue.connectionId,
              topic,
              "CONSUME",
              { rawMessage: message.value?.toString() },
              400,
              `Failed to parse message: ${parseError}`
            );
            return;
          }

          // Get the handler for this topic
          const handler = this.handlers.get(topic);
          if (!handler) {
            logger.error(`No handler registered for topic ${topic}`);
            return;
          }

          // Only log in debug mode
          logger.debug(
            `📨 Message received: ${topic}[${partition}]:${message.offset}`
          );

          try {
            // Log the message in the database
            await this.logMessageToDb(
              sourceId,
              messageValue.connectionId,
              topic,
              "CONSUME",
              messageValue,
              202,
              "Message received, processing started"
            );

            // Process the message
            const result = await handler(messageValue, sourceId);

            // Send response if a response topic is configured
            const responseTopic = this.responseTopics.get(topic);
            if (responseTopic && result) {
              await baseProducer.sendMessage(
                responseTopic,
                {
                  ...result,
                  metadata: {
                    sourceId,
                    correlationId: messageValue.metadata?.sourceId,
                    timestamp: new Date().toISOString(),
                  },
                },
                messageValue.connectionId
              );
            }

            // Update the log with success status
            await this.logMessageToDb(
              sourceId,
              messageValue.connectionId,
              topic,
              "CONSUME",
              { request: messageValue, response: result },
              200,
              "Message processed successfully"
            );
          } catch (error) {
            logger.error(
              `Error processing message from topic ${topic}: ${error}`
            );

            // Update the log with error status
            await this.logMessageToDb(
              sourceId,
              messageValue.connectionId,
              topic,
              "CONSUME",
              { request: messageValue, error },
              500,
              `Error processing message: ${error}`
            );

            // Send error response if a response topic is configured
            const responseTopic = this.responseTopics.get(topic);
            if (responseTopic) {
              await baseProducer.sendMessage(
                responseTopic,
                {
                  error: {
                    message: `Failed to process message: ${error}`,
                    code: "PROCESSING_ERROR",
                  },
                  metadata: {
                    sourceId,
                    correlationId: messageValue.metadata?.sourceId,
                    timestamp: new Date().toISOString(),
                  },
                },
                messageValue.connectionId
              );
            }
          }
        },
      });

      // Message processor started - no need to log
    } catch (error) {
      logger.error(`Failed to start message processor: ${error}`);
      throw error;
    }
  }

  /**
   * Log Kafka message details to the database
   */
  private async logMessageToDb(
    sourceId: string,
    connectionId: string | undefined,
    topic: string,
    action: string,
    details: any,
    status: number,
    message: string
  ): Promise<void> {
    try {
      await prisma.requestLog.create({
        data: {
          sourceId,
          connectionId,
          logType: LogType.KAFKA,
          endpointOrTopic: topic,
          methodOrAction: action,
          executionTime: new Date(),
          details,
          status,
          message,
        },
      });
    } catch (dbError) {
      logger.error(`Failed to log Kafka message to database: ${dbError}`);
    }
  }

  /**
   * Disconnect the consumer
   */
  async disconnect(): Promise<void> {
    if (this.consumer && this.isConnected) {
      try {
        await this.consumer.disconnect();
        this.isConnected = false;
        logger.info("Consumer disconnected successfully");
      } catch (error) {
        logger.error(`Failed to disconnect consumer: ${error}`);
      }
    }
  }
}

// Create a singleton instance
const baseConsumer = new BaseConsumer();
export default baseConsumer;
