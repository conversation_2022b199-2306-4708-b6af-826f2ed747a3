{"$schema": "http://json-schema.org/draft-07/schema#", "title": "<PERSON><PERSON><PERSON>", "description": "Schema for Vendor entities sent to Kafka in unified format", "type": "object", "required": ["id", "vendorName", "isActive", "domain"], "properties": {"id": {"type": "string", "description": "Unique identifier for the vendor"}, "vendorName": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Name of the vendor"}, "contactName": {"type": ["string", "null"], "description": "Primary contact name for the vendor"}, "email": {"type": ["string", "null"], "format": "email", "description": "Email address of the vendor"}, "phone": {"type": ["string", "null"], "description": "Phone number of the vendor"}, "website": {"type": ["string", "null"], "format": "uri", "description": "Website URL of the vendor"}, "bankAccountNumber": {"type": ["string", "null"], "description": "Bank account number for the vendor"}, "addresses": {"type": ["array", "null"], "description": "Array of vendor addresses", "items": {"type": "object", "properties": {"address1": {"type": ["string", "null"], "description": "Primary address line"}, "city": {"type": ["string", "null"], "description": "City name"}, "postalCode": {"type": ["string", "null"], "description": "Postal/ZIP code"}, "region": {"type": ["string", "null"], "description": "State/Province/Region"}, "country": {"type": ["string", "null"], "description": "Country name or code"}}}}, "isActive": {"type": "boolean", "description": "MANDATORY - Required by ALL platforms. Whether the vendor is active"}, "balance": {"type": ["number", "null"], "description": "Current balance owed to the vendor"}, "taxNumber": {"type": ["string", "null"], "description": "Tax identification number"}, "currencyCode": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Currency code (e.g., USD, EUR)"}, "paymentTermsId": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Payment terms identifier"}, "defaultExpenseAccountId": {"type": ["string", "null"], "description": "Optional - Required by: QBO, NetSuite only. Default expense account ID"}, "vendorType": {"type": ["string", "null"], "description": "Optional - Required by: NetSuite only. Type of vendor"}, "creditLimit": {"type": ["number", "null"], "description": "Optional - Required by: NetSuite only. Credit limit for the vendor"}, "externalId": {"type": ["string", "null"], "description": "External ID provided by the accounting platform"}, "lastSyncedAt": {"type": ["string", "null"], "format": "date-time", "description": "Last sync timestamp"}, "platformUrl": {"type": ["string", "null"], "description": "Deep link URL to the platform"}, "createdAtPlatform": {"type": "string", "format": "date-time", "description": "Creation timestamp from the platform"}, "updatedAtPlatform": {"type": "string", "format": "date-time", "description": "Last update timestamp from the platform"}, "domain": {"type": "string", "description": "Platform domain identifier"}}, "examples": [{"id": "123", "vendorName": "ABC Office Supplies", "contactName": "<PERSON>", "email": "<EMAIL>", "phone": "******-123-4567", "website": "https://www.abcoffice.com", "bankAccountNumber": null, "addresses": [{"address1": "123 Business St", "city": "New York", "postalCode": "10001", "region": "NY", "country": "USA"}], "isActive": true, "balance": 2500.75, "taxNumber": "12-3456789", "currencyCode": "USD", "paymentTermsId": "NET30", "defaultExpenseAccountId": "456", "vendorType": "Supplier", "creditLimit": 10000.0, "externalId": "123", "lastSyncedAt": "2024-01-15T10:30:00Z", "platformUrl": "https://qbo.intuit.com/app/vendor/123", "createdAtPlatform": "2024-01-01T09:00:00Z", "updatedAtPlatform": "2024-01-15T10:30:00Z", "domain": "qbo"}], "kafkaMessages": {"syncMessage": {"description": "Vendor data sent during sync operations (outbound from unified backend)", "topic": "entity-batch-stream", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "vendor", "operation": "FETCH"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": {"data": [{"id": "db-uuid-vendor-123", "externalId": "123", "vendorName": "ABC Office Supplies", "isActive": true, "domain": "qbo"}], "entityType": "vendor", "batchNumber": 1, "totalBatches": 1, "batchSize": 1000}, "status": {"code": "200", "message": "Data batch 1/1 for vendor"}}}, "createRequestMessage": {"description": "Vendor creation request (inbound to unified backend)", "topic": "entity-create-request", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "ZACT_APP", "destination": "UNIFIED_BACKEND", "messageType": "REQUEST", "erpSystem": "QBO", "entityOperation": {"entityType": "vendor", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": [{"vendorName": "ABC Office Supplies", "isActive": true, "domain": "qbo"}], "status": null}}, "createResponseMessage": {"description": "Vendor creation response (outbound from unified backend)", "topic": "entity-create-response", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": 1705312300000, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "vendor", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": {"data": {"id": "db-uuid-123", "externalId": "123", "vendorName": "ABC Office Supplies", "isActive": true, "domain": "qbo"}}, "status": {"code": "200", "message": "<PERSON><PERSON><PERSON> created successfully"}}}}}