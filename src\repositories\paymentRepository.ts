import prisma from "../config/db";
import logger from "../utils/logger";
import { IUnifiedPayment } from "../interfaces";

/**
 * Repository for Payment database operations
 */
export class PaymentRepository {
  /**
   * Save payment to database
   */
  static async save(
    unifiedPayment: IUnifiedPayment,
    connectionId: string
  ): Promise<any> {
    try {
      // Validate input
      if (!unifiedPayment) {
        throw new Error("Unified payment data is null or undefined");
      }

      logger.info(
        `Processing unified payment data:`,
        JSON.stringify(unifiedPayment, null, 2)
      );

      // Prepare data for database insertion
      const paymentData = {
        connectionId: connectionId,
        externalPaymentId:
          unifiedPayment.externalPaymentId || unifiedPayment.id,
        vendorId: unifiedPayment.vendorId,
        paymentType: unifiedPayment.paymentType
          .toUpperCase()
          .replace("CREDITCARD", "CREDIT_CARD") as any,
        totalAmount: unifiedPayment.totalAmount,
        paymentDate: new Date(unifiedPayment.paymentDate),
        currency: unifiedPayment.currency,
        bankAccountId: unifiedPayment.bankAccountId,
        creditCardAccountId: unifiedPayment.creditCardAccountId,
        checkNumber: unifiedPayment.checkNumber,
        privateNote: unifiedPayment.privateNote,
        billPayments: unifiedPayment.billPayments,
        domain: unifiedPayment.domain,
        createdAtPlatform: unifiedPayment.createdAtPlatform,
        updatedAtPlatform: unifiedPayment.updatedAtPlatform,
      };

      // Save to database
      const savedEntity = await prisma.payment.create({
        data: paymentData,
      });

      logger.info(`Payment saved to database with ID: ${savedEntity.id}`);

      // Return saved entity in unified response format with camelCase
      return {
        id: savedEntity.id,
        externalPaymentId: savedEntity.externalPaymentId,
        vendorId: savedEntity.vendorId,
        paymentType: savedEntity.paymentType,
        totalAmount: savedEntity.totalAmount,
        paymentDate: savedEntity.paymentDate.toISOString(),
        currency: savedEntity.currency,
        bankAccountId: savedEntity.bankAccountId,
        creditCardAccountId: savedEntity.creditCardAccountId,
        checkNumber: savedEntity.checkNumber,
        privateNote: savedEntity.privateNote,
        billPayments: savedEntity.billPayments,
        domain: savedEntity.domain,
        createdAtPlatform: savedEntity.createdAtPlatform.toISOString(),
        updatedAtPlatform: savedEntity.updatedAtPlatform?.toISOString(),
        connectionId: savedEntity.connectionId,
        createdAt: savedEntity.lastSyncedAt.toISOString(),
      };
    } catch (error) {
      logger.error(`Error saving Payment to database:`, error);
      throw error;
    }
  }

  /**
   * Find payments by connection ID
   */
  static async findByConnectionId(connectionId: string) {
    return await prisma.payment.findMany({
      where: { connectionId },
    });
  }

  /**
   * Find payment by external ID
   */
  static async findByExternalId(externalId: string, connectionId: string) {
    return await prisma.payment.findFirst({
      where: {
        externalPaymentId: externalId,
        connectionId,
      },
    });
  }

  /**
   * Update payment
   */
  static async update(id: string, data: any) {
    return await prisma.payment.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete old payments
   */
  static async deleteOld(connectionId: string, days: number = 90) {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return await prisma.payment.deleteMany({
      where: { connectionId, lastSyncedAt: { lt: cutoff } },
    });
  }
}
