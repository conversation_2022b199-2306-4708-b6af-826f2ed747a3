{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Account <PERSON><PERSON><PERSON>", "description": "Schema for Account entities sent to Kafka in unified format", "type": "object", "required": ["id", "fullyQualifiedName", "name", "accountType", "active", "currentBalance", "subAccount", "domain"], "properties": {"id": {"type": "string", "description": "Unique identifier for the account"}, "fullyQualifiedName": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Full hierarchical name of the account"}, "name": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Display name of the account"}, "accountType": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Type of account (e.g., Asset, Liability, Equity, Income, Expense)"}, "accountSubType": {"type": ["string", "null"], "description": "Sub-type classification of the account"}, "category": {"type": ["string", "null"], "description": "Category classification of the account"}, "parentId": {"type": ["string", "null"], "description": "ID of the parent account if this is a sub-account"}, "active": {"type": "boolean", "description": "MANDATORY - Required by ALL platforms. Whether the account is active"}, "currentBalance": {"type": "number", "description": "MANDATORY - Required by ALL platforms. Current balance of the account"}, "subAccount": {"type": "boolean", "description": "Whether this account is a sub-account of another account"}, "currentBalanceWithSubAccounts": {"type": ["number", "null"], "description": "Current balance including sub-accounts"}, "currencyCode": {"type": ["string", "null"], "description": "Currency code for the account (e.g., USD, EUR)"}, "accountNumber": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Account number"}, "description": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Account description"}, "taxCodeId": {"type": ["string", "null"], "description": "Optional - Required by: Xero, NetSuite (not QBO, QBD). Tax code identifier"}, "bankAccountNumber": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Bank account number"}, "routingNumber": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Bank routing number"}, "openingBalance": {"type": ["number", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Opening balance"}, "openingBalanceDate": {"type": ["string", "null"], "format": "date", "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Opening balance date"}, "externalId": {"type": ["string", "null"], "description": "External ID provided by the accounting platform"}, "lastSyncedAt": {"type": ["string", "null"], "format": "date-time", "description": "Last sync timestamp"}, "platformUrl": {"type": ["string", "null"], "description": "Deep link URL to the platform"}, "createdAtPlatform": {"type": "string", "format": "date-time", "description": "Creation timestamp from the platform"}, "updatedAtPlatform": {"type": "string", "format": "date-time", "description": "Last update timestamp from the platform"}, "domain": {"type": "string", "description": "Platform domain identifier"}}, "examples": [{"id": "1", "fullyQualifiedName": "Assets:Current Assets:Checking Account", "name": "Checking Account", "accountType": "Bank", "accountSubType": "Checking", "category": "Assets", "parentId": null, "active": true, "currentBalance": 15000.5, "subAccount": false, "currentBalanceWithSubAccounts": 15000.5, "currencyCode": "USD", "accountNumber": "1001", "description": "Primary business checking account", "taxCodeId": null, "bankAccountNumber": "*********", "routingNumber": "*********", "openingBalance": 10000.0, "openingBalanceDate": "2024-01-01", "externalId": "1", "lastSyncedAt": "2024-01-15T10:30:00Z", "platformUrl": "https://qbo.intuit.com/app/account/1", "createdAtPlatform": "2024-01-01T09:00:00Z", "updatedAtPlatform": "2024-01-15T10:30:00Z", "domain": "qbo"}], "kafkaMessages": {"syncMessage": {"description": "Account data sent during sync operations (outbound from unified backend)", "topic": "entity-batch-stream", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "account", "operation": "FETCH"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": {"data": [{"id": "db-uuid-account-1", "externalId": "1", "fullyQualifiedName": "Assets:Current Assets:Checking Account", "name": "Checking Account", "accountType": "Bank", "active": true, "currentBalance": 15000.5, "subAccount": false, "domain": "qbo"}], "entityType": "account", "batchNumber": 1, "totalBatches": 1, "batchSize": 1000}, "status": {"code": "200", "message": "Data batch 1/1 for account"}}}}}