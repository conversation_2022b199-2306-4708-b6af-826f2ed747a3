services:
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: zact-unified-backend-dev
    ports:
      - "8080:8080"
      - "9229:9229"
    environment:
      - NODE_ENV=development
      - PORT=8080
      - DATABASE_URL=mysql://zact_dev:devpassword@mysql-dev:3306/zact_unified_dev
      - KAFKA_BROKERS=kafka-dev:29092
      - KAFKA_CLIENT_ID=zact-unified-backend-dev
      - QBO_CLIENT_ID=${QBO_CLIENT_ID}
      - QBO_CLIENT_SECRET=${QBO_CLIENT_SECRET}
      - API_GATEWAY_URL=${API_GATEWAY_URL}
      - JWT_SECRET=${JWT_SECRET}
      - INCREMENTAL_SYNC_CRON_ENABLED=false
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    depends_on:
      - mysql-dev
      - kafka-dev

  mysql-dev:
    image: mysql:latest
    container_name: zact-mysql-dev
    environment:
      MYSQL_ROOT_PASSWORD: devpassword
      MYSQL_DATABASE: zact_unified_dev
      MYSQL_USER: zact_dev
      MYSQL_PASSWORD: devpassword
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql

  zookeeper-dev:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka-dev:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper-dev
    ports:
      - "9093:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-dev:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-dev:29092,PLAINTEXT_HOST://localhost:9093
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    volumes:
      - kafka_dev_data:/var/lib/kafka/data

  # Optional development tools
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: zact-kafka-ui
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka-dev:29092
    depends_on:
      - kafka-dev
    profiles: ["tools"]

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: zact-phpmyadmin
    ports:
      - "8082:80"
    environment:
      PMA_HOST: mysql-dev
      PMA_USER: root
      PMA_PASSWORD: devpassword
    depends_on:
      - mysql-dev
    profiles: ["tools"]

volumes:
  mysql_dev_data:
  kafka_dev_data:
