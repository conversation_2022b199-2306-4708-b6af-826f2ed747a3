{"name": "zact_unified_backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "npx nodemon src/index.ts --watch src --legacy-watch", "test": "jest", "lint": "eslint . --ext .ts"}, "keywords": ["kafka", "microservice", "express"], "author": "", "license": "ISC", "description": "Unified backend service with Kafka integration", "dependencies": {"@prisma/client": "^6.9.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "colors": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "exponential-backoff": "^3.1.2", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "md5": "^2.3.0", "mysql2": "^3.14.2", "node-cron": "^4.1.0", "nodemon": "^3.1.10", "p-limit": "^6.2.0", "prom-client": "^15.1.3", "ts-node": "^10.9.2", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/express-rate-limit": "^6.0.2", "@types/express-validator": "^3.0.2", "@types/helmet": "^4.0.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/kafkajs": "^1.9.0", "@types/md5": "^2.3.5", "@types/node": "^22.15.30", "@types/node-cron": "^3.0.11", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "jest": "^30.0.4", "prisma": "^6.9.0", "ts-jest": "^29.4.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}}