#!/usr/bin/env node

/**
 * Zact Unified Backend - Setup Validation Script
 * This script validates that the local development environment is properly configured
 */

const axios = require('axios');
const mysql = require('mysql2/promise');
const { Kafka } = require('kafkajs');

// Load environment variables
require('dotenv').config();

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) { log('cyan', `ℹ️  ${message}`); }
function logSuccess(message) { log('green', `✅ ${message}`); }
function logWarning(message) { log('yellow', `⚠️  ${message}`); }
function logError(message) { log('red', `❌ ${message}`); }

// Parse DATABASE_URL
function parseDatabaseUrl(url) {
    const regex = /mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
    const match = url.match(regex);
    
    if (!match) {
        throw new Error('Invalid DATABASE_URL format');
    }
    
    return {
        user: match[1],
        password: match[2],
        host: match[3],
        port: parseInt(match[4]),
        database: match[5]
    };
}

// Validate database connection
async function validateDatabase() {
    logInfo('Validating database connection...');
    
    try {
        if (!process.env.DATABASE_URL) {
            throw new Error('DATABASE_URL not set');
        }
        
        const config = parseDatabaseUrl(process.env.DATABASE_URL);
        const connection = await mysql.createConnection(config);
        
        // Test basic query
        await connection.execute('SELECT 1 as test');
        
        // Check if tables exist (basic Prisma check)
        const [tables] = await connection.execute(
            'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ?',
            [config.database]
        );
        
        await connection.end();
        
        logSuccess(`Database connection successful (${tables.length} tables found)`);
        return true;
    } catch (error) {
        logError(`Database validation failed: ${error.message}`);
        return false;
    }
}

// Validate Kafka connection
async function validateKafka() {
    logInfo('Validating Kafka connection...');
    
    try {
        const kafka = new Kafka({
            clientId: process.env.KAFKA_CLIENT_ID || 'validation-client',
            brokers: [process.env.KAFKA_BROKERS || 'localhost:9092'],
            connectionTimeout: 5000,
            requestTimeout: 10000
        });
        
        const admin = kafka.admin();
        await admin.connect();
        
        // List topics to verify connection
        const topics = await admin.listTopics();
        await admin.disconnect();
        
        logSuccess(`Kafka connection successful (${topics.length} topics found)`);
        return true;
    } catch (error) {
        logError(`Kafka validation failed: ${error.message}`);
        return false;
    }
}

// Validate Kafka UI
async function validateKafkaUI() {
    logInfo('Validating Kafka UI...');
    
    try {
        const response = await axios.get('http://localhost:8090', {
            timeout: 5000,
            validateStatus: (status) => status < 500
        });
        
        if (response.status === 200) {
            logSuccess('Kafka UI is accessible at http://localhost:8090');
            return true;
        } else {
            logWarning(`Kafka UI returned status ${response.status}`);
            return false;
        }
    } catch (error) {
        logError(`Kafka UI validation failed: ${error.message}`);
        return false;
    }
}

// Validate required topics
async function validateTopics() {
    logInfo('Validating Kafka topics...');
    
    const requiredTopics = [
        'entity-create-request',
        'entity-create-response',
        'entity-batch-stream',
        'sync-completion-response'
    ];
    
    try {
        const kafka = new Kafka({
            clientId: process.env.KAFKA_CLIENT_ID || 'validation-client',
            brokers: [process.env.KAFKA_BROKERS || 'localhost:9092'],
            connectionTimeout: 5000,
            requestTimeout: 10000
        });
        
        const admin = kafka.admin();
        await admin.connect();
        
        const topics = await admin.listTopics();
        await admin.disconnect();
        
        const missingTopics = requiredTopics.filter(topic => !topics.includes(topic));
        
        if (missingTopics.length === 0) {
            logSuccess(`All required topics exist (${requiredTopics.length}/${requiredTopics.length})`);
            return true;
        } else {
            logWarning(`Missing topics: ${missingTopics.join(', ')}`);
            logInfo('You can create them with: npm run infra:start');
            return false;
        }
    } catch (error) {
        logError(`Topic validation failed: ${error.message}`);
        return false;
    }
}

// Validate environment variables
function validateEnvironment() {
    logInfo('Validating environment variables...');
    
    const requiredVars = [
        'DATABASE_URL',
        'KAFKA_BROKERS',
        'KAFKA_CLIENT_ID',
        'PORT',
        'API_GATEWAY_URL'
    ];
    
    const missing = requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length === 0) {
        logSuccess('All required environment variables are set');
        return true;
    } else {
        logError(`Missing environment variables: ${missing.join(', ')}`);
        return false;
    }
}

// Main validation function
async function validateSetup() {
    console.log('🔍 Zact Unified Backend - Setup Validation');
    console.log('==========================================');
    
    const results = {
        environment: false,
        database: false,
        kafka: false,
        kafkaUI: false,
        topics: false
    };
    
    // Run all validations
    results.environment = validateEnvironment();
    results.database = await validateDatabase();
    results.kafka = await validateKafka();
    results.kafkaUI = await validateKafkaUI();
    results.topics = await validateTopics();
    
    // Summary
    console.log('\n📊 Validation Summary:');
    console.log('======================');
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    Object.entries(results).forEach(([component, passed]) => {
        const status = passed ? '✅' : '❌';
        const name = component.charAt(0).toUpperCase() + component.slice(1);
        console.log(`${status} ${name}`);
    });
    
    console.log(`\n📈 Overall: ${passed}/${total} checks passed`);
    
    if (passed === total) {
        logSuccess('\n🎉 All validations passed! Your development environment is ready.');
        logInfo('You can now run: npm run dev');
    } else {
        logError('\n❌ Some validations failed. Please check the errors above.');
        logInfo('Try running: npm run setup:full');
    }
    
    return passed === total;
}

// Run validation if called directly
if (require.main === module) {
    validateSetup()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            logError(`Validation error: ${error.message}`);
            process.exit(1);
        });
}

module.exports = { validateSetup };
