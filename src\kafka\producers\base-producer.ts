import { Producer } from "kafkajs";
import { v4 as uuidv4 } from "uuid";
import logger from "../../utils/logger";
import { createProducer } from "../config/kafka-config";
import prisma from "../../config/db";
import { LogType } from "@prisma/client";

class BaseProducer {
  private producer: Producer | null = null;
  private isConnected: boolean = false;

  async initialize() {
    if (this.isConnected && this.producer) {
      return; // Already initialized
    }

    try {
      this.producer = await createProducer();
      this.isConnected = true;
      // Logging moved to main kafka init
    } catch (error) {
      logger.error(`❌ Producer initialization failed: ${error}`);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Send a message to a Kafka topic
   * @param topic The Kafka topic to send the message to
   * @param message The message to send
   * @param connectionId Optional connection ID for logging
   * @returns Promise<void>
   */
  async sendMessage(
    topic: string,
    message: any,
    connectionId?: string
  ): Promise<void> {
    if (!this.isConnected || !this.producer) {
      throw new Error("Producer not initialized. Call initialize() first.");
    }

    const sourceId = uuidv4();
    const timestamp = new Date().toISOString();

    try {
      // Prepare the message with metadata
      const messageWithMetadata = {
        ...message,
      };

      // Send the message
      await this.producer.send({
        topic,
        messages: [
          {
            key: sourceId,
            value: JSON.stringify(messageWithMetadata),
            headers: {
              sourceId,
              timestamp,
            },
          },
        ],
      });

      logger.debug(`📤 Message sent: ${topic} [${sourceId}]`);

      // Log the message in the database
      // For Kafka error/success messages, store the full message object
      const detailsToStore =
        topic === "sync-completion-response" ||
        topic === "entity-create-response"
          ? messageWithMetadata
          : messageWithMetadata.status;

      await this.logMessageToDb(
        sourceId,
        connectionId,
        topic,
        "PRODUCE",
        detailsToStore,
        200,
        "Message sent successfully"
      );
    } catch (error) {
      logger.error(`Failed to send message to topic ${topic}: ${error}`);

      // Log the error in the database
      // For Kafka error/success messages, store the full message object even on send failure
      const detailsToStore =
        topic === "sync-completion-response" ||
        topic === "entity-create-response"
          ? message
          : `Send failed: ${error}`;

      await this.logMessageToDb(
        sourceId,
        connectionId,
        topic,
        "PRODUCE",
        detailsToStore,
        500,
        `Failed to send message: ${error}`
      );

      throw error;
    }
  }

  /**
   * Log Kafka message details to the database
   */
  private async logMessageToDb(
    sourceId: string,
    connectionId: string | undefined,
    topic: string,
    action: string,
    details: any,
    status: number,
    message: string
  ): Promise<void> {
    try {
      await prisma.requestLog.create({
        data: {
          sourceId,
          connectionId,
          logType: LogType.KAFKA,
          endpointOrTopic: topic,
          methodOrAction: action,
          executionTime: new Date(),
          details,
          status,
          message,
        },
      });
    } catch (dbError) {
      logger.error(`Failed to log Kafka message to database: ${dbError}`);
    }
  }

  /**
   * Disconnect the producer
   */
  async disconnect(): Promise<void> {
    if (this.producer && this.isConnected) {
      try {
        await this.producer.disconnect();
        this.isConnected = false;
        logger.info("Producer disconnected successfully");
      } catch (error) {
        logger.error(`Failed to disconnect producer: ${error}`);
      }
    }
  }
}

// Create a singleton instance
const baseProducer = new BaseProducer();
export default baseProducer;
