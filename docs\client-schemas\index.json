{"title": "Zact Unified Backend - Client <PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "JSON schemas and examples for all entity types supported by the Zact Unified Backend Kafka messaging system", "lastUpdated": "2024-01-17", "schemas": {"kafka-message": {"file": "kafka-message-schema.json", "title": "Kafka Message Schema", "description": "Complete structure for Kafka messages sent to entity-create-request topic", "topics": ["entity-create-request"], "required": true}, "account": {"file": "account-schema.json", "title": "Account <PERSON><PERSON><PERSON>", "description": "Chart of accounts and financial account entities", "entityType": "account", "mandatoryFields": ["id", "fullyQualifiedName", "name", "accountType", "active", "currentBalance", "subAccount", "domain"], "platforms": ["QBO", "Xero", "NetSuite"]}, "vendor": {"file": "vendor-schema.json", "title": "<PERSON><PERSON><PERSON>", "description": "Vendor/supplier information and contact details", "entityType": "vendor", "mandatoryFields": ["id", "vendorName", "isActive", "domain"], "platforms": ["QBO", "Xero", "NetSuite"]}, "class": {"file": "class-schema.json", "title": "Class Entity Schema", "description": "Class/department tracking for expenses and income categorization", "entityType": "class", "mandatoryFields": ["id", "name", "status", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdAtPlatform", "updatedAtPlatform", "domain"], "platforms": ["QBO", "Xero", "NetSuite"]}, "bill": {"file": "bill-schema.json", "title": "<PERSON>", "description": "Bills/invoices received from vendors", "entityType": "bill", "mandatoryFields": ["id", "vendorId", "bill<PERSON><PERSON><PERSON>", "billDate", "dueDate", "totalAmount", "balance", "currency", "status", "lineItems"], "platforms": ["QBO", "Xero", "NetSuite"], "validation": "validateBillRequest"}, "payment": {"file": "payment-schema.json", "title": "Payment Entity Schema", "description": "Payments made to vendors for bills", "entityType": "payment", "mandatoryFields": ["id", "vendorId", "paymentType", "totalAmount", "paymentDate", "currency", "billPayments"], "platforms": ["QBO", "Xero", "NetSuite"], "validation": "validatePaymentRequest", "paymentTypes": ["Check", "CreditCard", "Cash"]}, "journal-entry": {"file": "journal-entry-schema.json", "title": "Journal Entry En<PERSON><PERSON>", "description": "Manual journal entries for accounting adjustments", "entityType": "journal_entry", "mandatoryFields": ["id", "domain", "transactionDate", "lineItems"], "platforms": ["QBO", "Xero", "NetSuite"], "validation": "validateJournalEntryRequest", "notes": "Line items must balance (debits = credits), minimum 2 line items required"}}, "kafkaTopics": {"entity-create-request": {"description": "Topic for sending entity creation requests", "messageType": "REQUEST", "supportedEntities": ["account", "vendor", "class", "bill", "payment", "journal_entry"]}, "entity-create-response": {"description": "Topic for receiving entity creation responses", "messageType": "RESPONSE", "responseTypes": ["success", "validation_error", "api_error"]}}, "erpSystems": {"QBO": {"name": "QuickBooks Online", "code": "QBO", "supportedEntities": ["account", "vendor", "class", "bill", "payment", "journal_entry"]}, "XERO": {"name": "Xero", "code": "XERO", "supportedEntities": ["account", "vendor", "class", "bill", "payment", "journal_entry"]}, "NETSUITE": {"name": "Oracle NetSuite", "code": "NETSUITE", "supportedEntities": ["account", "vendor", "class", "bill", "payment", "journal_entry"]}}, "validationFunctions": {"validateVendorRequest": {"file": "src/validators/vendorValidator.ts", "description": "Validates vendor entity data"}, "validateBillRequest": {"file": "src/validators/billValidator.ts", "description": "Validates bill entity data including line items"}, "validatePaymentRequest": {"file": "src/validators/paymentValidator.ts", "description": "Validates payment entity data and bill payments"}, "validateJournalEntryRequest": {"file": "src/validators/journalEntryValidator.ts", "description": "Validates journal entry data and ensures balanced entries"}}, "examples": {"basicVendorCreation": {"description": "Simple vendor creation with minimal required fields", "schema": "vendor", "file": "vendor-schema.json"}, "billWithLineItems": {"description": "Bill creation with multiple line items", "schema": "bill", "file": "bill-schema.json"}, "checkPayment": {"description": "Check payment to vendor for specific bills", "schema": "payment", "file": "payment-schema.json"}, "balancedJournalEntry": {"description": "Journal entry with balanced debit and credit entries", "schema": "journal-entry", "file": "journal-entry-schema.json"}}, "usage": {"gettingStarted": "See README.md for detailed usage instructions", "validation": "Each entity type has validation functions in src/validators/", "testing": "Use the examples in each schema file for testing integration", "support": "Contact development team for integration assistance"}}