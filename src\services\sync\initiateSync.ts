import { SyncedEntityType, SyncOperationType } from ".prisma/client";
import {
  batchUpdateSyncOperations,
  syncEntity,
  updateSyncOperation,
  validateConnection,
} from "../../utils/syncUtils";
import pLimit from "p-limit";
import logger from "../../utils/logger";

/**
 * Initiate full sync with connection validation and batch updates
 * @param service - The service name (e.g., 'qbo', 'xero')
 * @param connectionId - The ID of the accounting platform integration
 * @param accessToken - The access token for API authentication
 * @param companyId - The company ID
 * @param syncOperationType - The type of sync operation (default: FULL_SYNC)
 */

// Import configuration
import { SYNC_CONFIG } from "../../constants/syncConfig";
import { envConfig } from "../../config/config";

// Configuration
const CONFIG = {
  MAX_CONCURRENT_SYNCS: envConfig.sync.maxConcurrentSyncs,
};

export const initiateFullSync = async (
  service: string,
  connectionId: string,
  accessToken: string,
  companyId: string,
  syncOperationType: SyncOperationType = SyncOperationType.FULL_SYNC
) => {
  // syncOperationType will be used for creating sync operations
  const connection = await validateConnection(connectionId);

  // Currently only syncing accounts for testing
  const entitiesToSync = [
    { name: "account", type: SyncedEntityType.ACCOUNT },
    { name: "vendor", type: SyncedEntityType.VENDOR },
    { name: "class", type: SyncedEntityType.CLASS },
  ];

  logger.info(`Starting full sync for ${entitiesToSync.length} entities`, {
    service,
    connectionId,
    companyName: connection.companyName,
  });

  const startTime = Date.now();

  // Create a limiter to control concurrency
  const limit = pLimit(CONFIG.MAX_CONCURRENT_SYNCS);

  // Create limited sync promises
  const syncPromises = entitiesToSync.map((entity) =>
    limit(() =>
      syncEntity(
        service,
        connectionId,
        accessToken,
        companyId,
        entity,
        syncOperationType
      )
    )
  );

  const settledResults = await Promise.allSettled(syncPromises);

  const results = settledResults.map((result, index) => {
    if (result.status === "fulfilled") {
      return result.value;
    } else {
      logger.error(
        `Critical error during sync of ${entitiesToSync[index].name}:`,
        { error: result.reason }
      );
      return {
        entity: entitiesToSync[index].name,
        status: "FATAL",
        error: result.reason?.message || "An unknown critical error occurred.",
        updateData: null, // No update data for fatal errors
      };
    }
  });

  // Batch update all sync operations at once
  const updateOperations = results
    .filter(
      (result) => result.updateData !== null && result.updateData !== undefined
    ) // Only results with valid update data
    .map((result) => ({
      syncId: result.syncId,
      data: result.updateData,
    }));

  if (updateOperations.length > 0) {
    try {
      logger.info(`Batch updating ${updateOperations.length} sync operations`);
      await batchUpdateSyncOperations(updateOperations);
      logger.info(
        `Successfully batch updated ${updateOperations.length} sync operations`
      );
    } catch (error) {
      logger.error(
        `Failed to batch update sync operations, falling back to individual updates:`,
        error
      );

      // Fallback: Update individually if batch update fails
      for (const operation of updateOperations) {
        try {
          await updateSyncOperation(operation.syncId, operation.data);
        } catch (individualError) {
          logger.error(
            `Failed to update sync operation ${operation.syncId}:`,
            individualError
          );
        }
      }
    }
  }

  const totalDuration = Date.now() - startTime;
  const summary = {
    totalEntities: results.length,
    completed: results.filter((r) => r.status === "COMPLETED").length,
    failed: results.filter((r) => r.status === "FAILED").length,
    fatal: results.filter((r) => r.status === "FATAL").length,
    totalDuration,
    averageEntityTime: results.length > 0 ? totalDuration / results.length : 0,
    batchUpdated: updateOperations.length,
  };

  logger.info("Full sync completed", summary);

  // Clean up the results for return (remove updateData)
  const cleanResults = results.map(({ updateData, ...result }) => result);

  return {
    results: cleanResults,
    summary,
  };
};
