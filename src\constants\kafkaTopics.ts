/**
 * Kafka topic constants
 */
export const K<PERSON><PERSON>_TOPICS = {
  // Entity operations
  ENTITY_CREATE_REQUEST: "entity-create-request",
  ENTITY_CREATE_RESPONSE: "entity-create-response",
  ENTITY_BATCH_STREAM: "entity-batch-stream",
  
  // Sync operations
  SYNC_COMPLETION_RESPONSE: "sync-completion-response",
  
  // Error handling
  SYNC_ERROR_TOPIC: "sync-completion-response",
} as const;

/**
 * Legacy topic constants for backward compatibility
 * @deprecated Use KAFKA_TOPICS instead
 */
export const TOPICS = {
  ENTITY_CREATE_REQUEST: KAFKA_TOPICS.ENTITY_CREATE_REQUEST,
  ENTITY_CREATE_RESPONSE: KAFKA_TOPICS.ENTITY_CREATE_RESPONSE,
} as const;
