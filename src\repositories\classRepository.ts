import prisma from "../config/db";
import logger from "../utils/logger";

/**
 * Repository for Class database operations
 */
export class ClassRepository {
  /**
   * Find classes by connection ID
   */
  static async findByConnectionId(connectionId: string) {
    return await prisma.class.findMany({
      where: { connectionId },
    });
  }

  /**
   * Find class by external ID
   */
  static async findByExternalId(externalId: string, connectionId: string) {
    return await prisma.class.findFirst({
      where: {
        externalClassId: externalId,
        connectionId,
      },
    });
  }

  /**
   * Create or update class
   */
  static async upsert(classData: any) {
    return await prisma.class.upsert({
      where: {
        connectionId_externalClassId: {
          connectionId: classData.connectionId,
          externalClassId: classData.externalClassId,
        },
      },
      update: {
        ...classData,
        lastSyncedAt: new Date(),
      },
      create: {
        ...classData,
        lastSyncedAt: new Date(),
      },
    });
  }

  /**
   * Bulk create classes
   */
  static async createMany(classes: any[]) {
    return await prisma.class.createMany({
      data: classes,
      skipDuplicates: true,
    });
  }

  /**
   * Update class
   */
  static async update(id: string, data: any) {
    return await prisma.class.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete old classes
   */
  static async deleteOld(connectionId: string, days: number = 90) {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return await prisma.class.deleteMany({
      where: { connectionId, lastSyncedAt: { lt: cutoff } },
    });
  }

  /**
   * Get count by connection ID
   */
  static async countByConnectionId(connectionId: string) {
    return await prisma.class.count({
      where: { connectionId },
    });
  }
}
