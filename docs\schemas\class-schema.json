{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Class Entity Schema", "description": "Schema for Class entities sent to Kafka in unified format", "type": "object", "required": ["id", "name", "status", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdAtPlatform", "updatedAtPlatform", "domain"], "properties": {"id": {"type": "string", "description": "Unique identifier for the class"}, "name": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Name of the class"}, "status": {"type": "boolean", "description": "MANDATORY - Required by ALL platforms. Whether the class is active/enabled"}, "hasChildren": {"type": "boolean", "description": "Whether this class has child classes"}, "parentId": {"type": ["string", "null"], "description": "ID of the parent class if this is a sub-class"}, "fullyQualifiedName": {"type": ["string", "null"], "description": "Optional - Required by: QBO, QBD (not Xero, NetSuite). Full hierarchical name"}, "classNumber": {"type": ["string", "null"], "description": "Optional - Required by: QBD, NetSuite only. Class number identifier"}, "description": {"type": ["string", "null"], "description": "Optional - Required by: NetSuite only. Description of the class"}, "externalId": {"type": ["string", "null"], "description": "External ID provided by the accounting platform"}, "lastSyncedAt": {"type": ["string", "null"], "format": "date-time", "description": "Last sync timestamp"}, "platformUrl": {"type": ["string", "null"], "description": "Deep link URL to the platform"}, "createdAtPlatform": {"type": "string", "format": "date-time", "description": "Creation timestamp from the platform"}, "updatedAtPlatform": {"type": "string", "format": "date-time", "description": "Last update timestamp from the platform"}, "domain": {"type": "string", "description": "Platform domain identifier"}}, "examples": [{"id": "1", "name": "Marketing", "status": true, "hasChildren": true, "parentId": null, "fullyQualifiedName": "Marketing", "classNumber": "MKT001", "description": "Marketing department expenses and activities", "externalId": "1", "lastSyncedAt": "2024-01-15T10:30:00Z", "platformUrl": "https://qbo.intuit.com/app/class/1", "createdAtPlatform": "2024-01-01T09:00:00Z", "updatedAtPlatform": "2024-01-15T10:30:00Z", "domain": "qbo"}, {"id": "2", "name": "Digital Marketing", "status": true, "hasChildren": false, "parentId": "1", "fullyQualifiedName": "Marketing:Digital Marketing", "classNumber": "MKT002", "description": "Digital marketing campaigns and online advertising", "externalId": "2", "lastSyncedAt": "2024-01-15T10:30:00Z", "platformUrl": "https://qbo.intuit.com/app/class/2", "createdAtPlatform": "2024-01-01T09:00:00Z", "updatedAtPlatform": "2024-01-15T10:30:00Z", "domain": "qbo"}], "kafkaMessages": {"syncMessage": {"description": "Class data sent during sync operations (outbound from unified backend)", "topic": "entity-batch-stream", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": 1705312200000, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "class", "operation": "FETCH"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-123-456-789"}, "payload": {"data": [{"id": "db-uuid-class-1", "externalId": "1", "name": "Marketing", "status": true, "hasChildren": true, "domain": "qbo"}], "entityType": "class", "batchNumber": 1, "totalBatches": 1, "batchSize": 1000}, "status": {"code": "200", "message": "Data batch 1/1 for class"}}}}}