export interface IQboJournalEntryResponse {
  Id: string;
  domain: string;
  TxnDate: string;
  CurrencyRef?: {
    value: string;
  };
  TotalAmt?: number;
  PrivateNote?: string; // QBO supports PrivateNote for journal entries
  Line: Array<{
    Id?: string;
    Amount: number;
    Description?: string;
    DetailType: string | "JournalEntryLineDetail";
    JournalEntryLineDetail?: {
      PostingType: "Debit" | "Credit";
      AccountRef: {
        value: string;
      };
    };
  }>;
  MetaData?: {
    CreateTime?: string;
    LastUpdatedTime?: string;
  };
}

export interface IUnifiedJournalEntry {
  id: string;
  domain: string;
  transactionDate: string; // MANDATORY - Required by ALL platforms
  totalAmount?: number;
  currencyCode: string | null;
  // MANDATORY - Required by ALL platforms (Sage Intacct requires memo as mandatory)
  memo?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter, Sage Intacct (mandatory)
  // Optional fields - Platform specific requirements
  exchangeRate?: number | null; // Required by: <PERSON>ero, <PERSON>Suite, Merge, Rutter (not QBO, QBD)
  journalNumber?: string | null; // Required by: Xero, QBD, NetSuite, Merge (not QBO, Rutter)
  postingStatus?: "POSTED" | "UNPOSTED" | null; // Required by: Xero, NetSuite, Merge (not QBO, QBD, Rutter)
  accountingPeriodId?: string | null; // Required by: NetSuite, Merge only
  inclusiveOfTax?: boolean | null; // Required by: NetSuite, Merge, Xero only
  companyId?: string | null; // Required by: NetSuite, Merge only
  // Sync tracking fields
  externalId?: string | null; // For platform-specific IDs
  lastSyncedAt?: Date | null; // For sync tracking
  platformUrl?: string | null; // For deep linking
  lineItems: Array<{
    postingType: "Debit" | "Credit";
    totalAmount: number; // MANDATORY - Required by ALL platforms
    accountId: string; // MANDATORY - Required by ALL platforms
    detailType: string;
    description?: string;
    // Optional line item fields - Platform specific requirements
    taxRateId?: string | null; // Required by: Xero, NetSuite, Merge, Rutter (not QBO, QBD)
    trackingCategories?: string[] | null; // Required by: QBO, Xero, QBD, NetSuite, Merge (not Rutter)
    employeeId?: string | null; // Required by: NetSuite, Merge only
    projectId?: string | null; // Required by: NetSuite, Merge only
    contactId?: string | null; // Required by: QBD, NetSuite, Merge only
    classId?: string | null; // Required by: QBO, QBD only (for tracking categories)
    departmentId?: string | null; // Required by: NetSuite only
    locationId?: string | null; // Required by: NetSuite only
  }>;
  createdAtPlatform: Date;
  updatedAtPlatform: Date;
}
