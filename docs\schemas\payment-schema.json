{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Payment Entity Schema", "description": "Schema for Payment entities sent to Kafka in unified format", "type": "object", "required": ["id", "vendorId", "paymentType", "totalAmount", "paymentDate", "currency", "billPayments"], "properties": {"id": {"type": "string", "description": "Unique identifier for the payment"}, "vendorId": {"type": "string", "description": "MANDATORY - Required by ALL platforms. ID of the vendor being paid"}, "externalPaymentId": {"type": ["string", "null"], "description": "External platform-specific payment ID"}, "paymentType": {"type": "string", "enum": ["Check", "CreditCard", "Cash"], "description": "MANDATORY - Required by ALL platforms. Type of payment method"}, "totalAmount": {"type": "number", "minimum": 0, "description": "MANDATORY - Required by ALL platforms. Total amount of the payment"}, "paymentDate": {"type": "string", "format": "date", "description": "MANDATORY - Required by ALL platforms. Date of payment (ISO date string)"}, "currency": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Currency code (e.g., USD, EUR)"}, "bankAccountId": {"type": ["string", "null"], "description": "Bank account ID (required for Check payments)"}, "creditCardAccountId": {"type": ["string", "null"], "description": "Credit card account ID (required for CreditCard payments)"}, "checkNumber": {"type": ["string", "null"], "description": "Check number (for Check payments)"}, "privateNote": {"type": ["string", "null"], "description": "Private note or memo for the payment"}, "referenceNumber": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Reference number"}, "exchangeRate": {"type": ["number", "null"], "description": "Optional - Required by: Xero, NetSuite (not QBO, QBD). Exchange rate"}, "paymentMethodId": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, NetSuite (not QBD). Payment method ID"}, "memo": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Memo field"}, "externalId": {"type": ["string", "null"], "description": "External ID provided by the accounting platform"}, "lastSyncedAt": {"type": ["string", "null"], "format": "date-time", "description": "Last sync timestamp"}, "platformUrl": {"type": ["string", "null"], "description": "Deep link URL to the platform"}, "billPayments": {"type": "array", "minItems": 1, "description": "MANDATORY - Array of bill payments this payment applies to", "items": {"type": "object", "required": ["billId", "amount"], "properties": {"billId": {"type": "string", "description": "MANDATORY - Required by ALL platforms. ID of the bill being paid"}, "amount": {"type": "number", "minimum": 0, "description": "MANDATORY - Required by ALL platforms. Amount applied to this bill"}, "discountAmount": {"type": ["number", "null"], "description": "Optional - Required by: QBO, Xero, NetSuite (not QBD). Discount amount"}}}}}, "examples": [{"id": "PAY001", "vendorId": "123", "externalPaymentId": "PAY001", "paymentType": "Check", "totalAmount": 1250.0, "paymentDate": "2024-01-20", "currency": "USD", "bankAccountId": "BANK001", "creditCardAccountId": null, "checkNumber": "1001", "privateNote": "Payment for office supplies", "referenceNumber": "REF-2024-001", "exchangeRate": null, "paymentMethodId": "CHECK_METHOD", "memo": "Q1 office supplies payment", "externalId": "PAY001", "lastSyncedAt": "2024-01-20T10:30:00Z", "platformUrl": "https://qbo.intuit.com/app/payment/PAY001", "billPayments": [{"billId": "BILL001", "amount": 1250.0, "discountAmount": 0.0}]}, {"id": "PAY002", "vendorId": "456", "externalPaymentId": "PAY002", "paymentType": "CreditCard", "totalAmount": 750.5, "paymentDate": "2024-01-22", "currency": "USD", "bankAccountId": null, "creditCardAccountId": "CC001", "checkNumber": null, "privateNote": "Software subscription payment", "referenceNumber": "REF-2024-002", "exchangeRate": null, "paymentMethodId": "CC_METHOD", "memo": "Monthly software subscription", "externalId": "PAY002", "lastSyncedAt": "2024-01-22T10:30:00Z", "platformUrl": "https://qbo.intuit.com/app/payment/PAY002", "billPayments": [{"billId": "BILL002", "amount": 750.5, "discountAmount": 25.0}]}], "kafkaMessages": {"createRequestMessage": {"description": "Payment creation request (inbound to unified backend)", "topic": "entity-create-request", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "ZACT_APP", "destination": "UNIFIED_BACKEND", "messageType": "REQUEST", "erpSystem": "QBO", "entityOperation": {"entityType": "payment", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-123-456-789"}, "payload": [{"vendorId": "123", "paymentType": "Check", "totalAmount": 1250.0, "paymentDate": "2024-01-20", "currency": "USD", "bankAccountId": "BANK001", "checkNumber": "1001", "billPayments": [{"billId": "BILL001", "amount": 1250.0}]}], "status": null}}, "createResponseMessage": {"description": "Payment creation response (outbound from unified backend)", "topic": "entity-create-response", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "payment", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-123-456-789"}, "payload": {"data": {"id": "db-uuid-pay-001", "externalId": "PAY001", "vendorId": "123", "paymentType": "Check", "totalAmount": 1250.0, "paymentDate": "2024-01-20", "currency": "USD"}}, "status": {"code": "200", "message": "Payment created successfully"}}}}}