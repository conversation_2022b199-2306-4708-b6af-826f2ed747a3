#!/usr/bin/env node

/**
 * Zact Unified Backend - Database Setup Script
 * This script checks and sets up the local MySQL database
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) { log('cyan', `ℹ️  ${message}`); }
function logSuccess(message) { log('green', `✅ ${message}`); }
function logWarning(message) { log('yellow', `⚠️  ${message}`); }
function logError(message) { log('red', `❌ ${message}`); }

// Parse DATABASE_URL
function parseDatabaseUrl(url) {
    const regex = /mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/;
    const match = url.match(regex);
    
    if (!match) {
        throw new Error('Invalid DATABASE_URL format');
    }
    
    return {
        user: match[1],
        password: match[2],
        host: match[3],
        port: parseInt(match[4]),
        database: match[5]
    };
}

// Test database connection
async function testConnection(config) {
    try {
        const connection = await mysql.createConnection({
            host: config.host,
            port: config.port,
            user: config.user,
            password: config.password
        });
        
        await connection.execute('SELECT 1');
        await connection.end();
        return true;
    } catch (error) {
        return false;
    }
}

// Check if database exists
async function checkDatabase(config) {
    try {
        const connection = await mysql.createConnection({
            host: config.host,
            port: config.port,
            user: config.user,
            password: config.password
        });
        
        const [rows] = await connection.execute(
            'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
            [config.database]
        );
        
        await connection.end();
        return rows.length > 0;
    } catch (error) {
        return false;
    }
}

// Create database
async function createDatabase(config) {
    try {
        const connection = await mysql.createConnection({
            host: config.host,
            port: config.port,
            user: config.user,
            password: config.password
        });
        
        await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${config.database}\``);
        await connection.end();
        return true;
    } catch (error) {
        logError(`Failed to create database: ${error.message}`);
        return false;
    }
}

// Check if Prisma is available
function checkPrisma() {
    const prismaPath = path.join(process.cwd(), 'node_modules', '.bin', 'prisma');
    const prismaPathWin = path.join(process.cwd(), 'node_modules', '.bin', 'prisma.cmd');
    
    return fs.existsSync(prismaPath) || fs.existsSync(prismaPathWin);
}

// Run Prisma commands
async function runPrismaCommand(command) {
    const { spawn } = require('child_process');
    
    return new Promise((resolve, reject) => {
        const isWindows = process.platform === 'win32';
        const prismaCmd = isWindows ? 'npx.cmd' : 'npx';
        const args = ['prisma', ...command.split(' ')];
        
        const child = spawn(prismaCmd, args, {
            stdio: 'inherit',
            cwd: process.cwd()
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Prisma command failed with code ${code}`));
            }
        });
        
        child.on('error', (error) => {
            reject(error);
        });
    });
}

// Main setup function
async function setupDatabase() {
    logInfo('🏗️  Zact Unified Backend - Database Setup');
    logInfo('==========================================');
    
    // Check if DATABASE_URL is set
    if (!process.env.DATABASE_URL) {
        logError('DATABASE_URL environment variable is not set');
        process.exit(1);
    }
    
    logInfo(`Using DATABASE_URL: ${process.env.DATABASE_URL}`);
    
    try {
        // Parse database configuration
        const dbConfig = parseDatabaseUrl(process.env.DATABASE_URL);
        logInfo(`Connecting to MySQL at ${dbConfig.host}:${dbConfig.port}`);
        
        // Test connection to MySQL server
        logInfo('Testing connection to MySQL server...');
        const canConnect = await testConnection(dbConfig);
        
        if (!canConnect) {
            logError('Cannot connect to MySQL server. Please ensure:');
            logError('1. MySQL server is running');
            logError('2. Connection details in DATABASE_URL are correct');
            logError('3. User has proper permissions');
            process.exit(1);
        }
        
        logSuccess('Connected to MySQL server successfully');
        
        // Check if database exists
        logInfo(`Checking if database '${dbConfig.database}' exists...`);
        const dbExists = await checkDatabase(dbConfig);
        
        if (!dbExists) {
            logWarning(`Database '${dbConfig.database}' does not exist`);
            logInfo('Creating database...');
            
            const created = await createDatabase(dbConfig);
            if (!created) {
                process.exit(1);
            }
            
            logSuccess(`Database '${dbConfig.database}' created successfully`);
        } else {
            logSuccess(`Database '${dbConfig.database}' already exists`);
        }
        
        // Check if Prisma is available
        if (!checkPrisma()) {
            logError('Prisma is not installed. Please run: npm install');
            process.exit(1);
        }
        
        // Run Prisma migrations
        logInfo('Running Prisma database migrations...');
        try {
            await runPrismaCommand('migrate deploy');
            logSuccess('Database migrations completed successfully');
        } catch (error) {
            logWarning('Migration failed, trying to generate Prisma client...');
            try {
                await runPrismaCommand('generate');
                logSuccess('Prisma client generated successfully');
                logWarning('You may need to run migrations manually: npx prisma migrate dev');
            } catch (genError) {
                logError(`Failed to generate Prisma client: ${genError.message}`);
            }
        }
        
        // Final connection test with database
        logInfo('Testing final database connection...');
        try {
            const connection = await mysql.createConnection({
                host: dbConfig.host,
                port: dbConfig.port,
                user: dbConfig.user,
                password: dbConfig.password,
                database: dbConfig.database
            });
            
            await connection.execute('SELECT 1');
            await connection.end();
            
            logSuccess('✅ Database setup completed successfully!');
            logInfo('🚀 Your local database is ready for development');
            
        } catch (error) {
            logError(`Final connection test failed: ${error.message}`);
            process.exit(1);
        }
        
    } catch (error) {
        logError(`Setup failed: ${error.message}`);
        process.exit(1);
    }
}

// Run the setup
if (require.main === module) {
    setupDatabase().catch((error) => {
        logError(`Unexpected error: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { setupDatabase };
