# MySQL Migration Guide

## Changes Made

### 1. Updated Prisma Schema
- Changed database provider from `postgresql` to `mysql` in `prisma/schema.prisma`
- The schema structure remains the same, only the provider changed

### 2. Installed MySQL Dependencies
- Added `mysql2` package as a dependency for MySQL connectivity

### 3. Updated Database URL
- Changed DATABASE_URL in `.env` from PostgreSQL format to MySQL format:
  - **Before**: `postgresql://postgres:12345678@localhost:5432/zact-staging?schema=public`
  - **After**: `mysql://root:12345678@localhost:3306/zact_staging`

### 4. Regenerated Prisma Client
- Successfully generated new Prisma client for MySQL compatibility

## Next Steps Required

### 1. Set Up MySQL Database
You need to create the MySQL database and user:

```sql
-- Connect to MySQL as root
CREATE DATABASE zact_staging;
CREATE USER 'root'@'localhost' IDENTIFIED BY '12345678';
GRANT ALL PRIVILEGES ON zact_staging.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 2. Run Database Migration
Execute the Prisma migration to create tables in MySQL:

```bash
npx prisma db push
```

Or if you want to create a proper migration:

```bash
npx prisma migrate dev --name init_mysql
```

### 3. Verify Connection
Test the database connection by running your application:

```bash
npm run dev
```

## Important Notes

### Database Differences
- **UUID Support**: MySQL uses `VARCHAR(36)` for UUID fields instead of native UUID type
- **JSON Fields**: MySQL 5.7+ supports JSON fields natively
- **Indexes**: All existing indexes should work with MySQL
- **Constraints**: Foreign key constraints are preserved

### Environment Variables
Make sure to update your production environment variables:
- Update `DATABASE_URL` to use MySQL connection string format
- Ensure MySQL server is running on port 3306 (default)

### Data Migration (if needed)
If you have existing data in PostgreSQL that needs to be migrated:
1. Export data from PostgreSQL
2. Transform data format if necessary
3. Import into MySQL database

## Troubleshooting

### Common Issues
1. **Connection refused**: Ensure MySQL server is running
2. **Authentication failed**: Verify username/password in DATABASE_URL
3. **Database doesn't exist**: Create the database first using SQL commands above

### Rollback Plan
If you need to rollback to PostgreSQL:
1. Change provider back to `postgresql` in schema.prisma
2. Update DATABASE_URL back to PostgreSQL format
3. Run `npx prisma generate` to regenerate client
4. Install `pg` package if removed: `npm install pg @types/pg`
