import { IQboPaymentResponse, IUnifiedPayment } from "../interfaces";

export const mapQboPaymentResponseToUnified = (
  response: IQboPaymentResponse
): IUnifiedPayment => {
  return {
    id: response.Id,
    vendorId: response.VendorRef?.value ?? "",
    externalPaymentId: response.Id,
    paymentType: response.PayType,
    totalAmount: response.TotalAmt,
    paymentDate: response.TxnDate ?? new Date().toISOString(),
    currency: response.CurrencyRef?.value ?? "USD",
    bankAccountId: response.CheckPayment?.BankAccountRef?.value ?? null,
    creditCardAccountId: response.CreditCardPayment?.CCAccountRef?.value ?? null,
    checkNumber: null, // QBO doesn't provide check number in response
    privateNote: response.PrivateNote ?? null,
    billPayments: response.Line.map((line) => ({
      billId: line.LinkedTxn?.[0]?.TxnId ?? "",
      amount: line.Amount,
    })),
    createdAtPlatform: response.MetaData?.CreateTime
      ? new Date(response.MetaData.CreateTime)
      : new Date(),
    updatedAtPlatform: response.MetaData?.LastUpdatedTime
      ? new Date(response.MetaData.LastUpdatedTime)
      : null,
    domain: response.domain,
  };
};

export const mapUnifiedToQboPaymentRequest = (
  payment: Partial<IUnifiedPayment>
): Partial<IQboPaymentResponse> => {
  const qboRequest: Partial<IQboPaymentResponse> = {
    VendorRef: payment.vendorId
      ? {
          value: payment.vendorId,
        }
      : undefined,
    PayType: payment.paymentType ?? "Check",
    TotalAmt: payment.totalAmount ?? 0,
    TxnDate: payment.paymentDate ?? new Date().toISOString().split("T")[0],
    CurrencyRef: payment.currency ? { value: payment.currency } : undefined,
    PrivateNote: payment.privateNote ?? undefined,
    Line:
    payment.billPayments?.map((billPayment) => ({
      Amount: billPayment.amount,
      LinkedTxn: [
          {
            TxnId: billPayment.billId,
            TxnType: "Bill",
          },
        ],
      })) ?? [],
    domain: payment.domain ?? "",
  };
  
  // Add payment method specific details
  if (payment.paymentType === "Check" && payment.bankAccountId) {
    qboRequest.CheckPayment = {
      BankAccountRef: {
        value: payment.bankAccountId,
      },
      PrintStatus: "NeedToPrint",
    };
  } else if (
    payment.paymentType === "CreditCard" &&
    payment.creditCardAccountId
  ) {
    qboRequest.CreditCardPayment = {
      CCAccountRef: {
        value: payment.creditCardAccountId,
      },
    };
  }


  return qboRequest;
};

/**
 * Map QBO Payment response to unified format and save to database
 * @deprecated Use PaymentRepository.save() instead
 */
export const mapAndSavePayment = async (
  qboPayment: any,
  connectionId: string
): Promise<any> => {
  // Import here to avoid circular dependencies
  const { PaymentRepository } = await import("../repositories");

  // Use existing mapper to convert QBO response to unified format
  const unifiedPayment = mapQboPaymentResponseToUnified(qboPayment);

  // Use repository to save
  return await PaymentRepository.save(unifiedPayment, connectionId);
};
