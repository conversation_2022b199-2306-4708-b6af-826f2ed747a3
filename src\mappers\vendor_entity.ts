import { IQboVendorResponse, IUnifiedVendor } from "../interfaces";

export const mapUnifiedToQboVendorRequest = (
  vendor: Partial<IUnifiedVendor>
): Partial<IQboVendorResponse> => {
  return {
    DisplayName: vendor.vendorName!,
    PrimaryEmailAddr: vendor.email
      ? {
          Address: vendor.email,
        }
      : undefined,
    PrimaryPhone: vendor.phone
      ? {
          FreeFormNumber: vendor.phone,
        }
      : undefined,
    WebAddr: vendor.website
      ? {
          URI: vendor.website,
        }
      : undefined,
    AcctNum: vendor.bankAccountNumber ?? undefined,
    Balance: vendor.balance ?? 0.0,
    BillAddr:
      vendor.addresses && vendor.addresses.length > 0
        ? {
            Line1: vendor.addresses[0].address1 ?? undefined,
            City: vendor.addresses[0].city ?? undefined,
            PostalCode: vendor.addresses[0].postalCode ?? undefined,
            CountrySubDivisionCode: vendor.addresses[0].region ?? undefined,
            Country: vendor.addresses[0].country ?? undefined,
          }
        : undefined,
    Active: vendor.isActive ?? true,
    TaxIdentifier: vendor.taxNumber ?? undefined,
    domain: vendor.domain ?? "",
  };
};

export const mapQboVendorResponseToUnified = (
  response: IQboVendorResponse
): IUnifiedVendor => {
  return {
    id: response.Id,
    vendorName: response.DisplayName,
    contactName: response.DisplayName, // same as vendorName per mapping
    email: response.PrimaryEmailAddr?.Address ?? null,
    phone: response.PrimaryPhone?.FreeFormNumber ?? null, // Fixed typo: was 'Freeform Number'
    website: response.WebAddr?.URI ?? null,
    bankAccountNumber: response.AcctNum ?? null,
    addresses: [
      {
        address1: response.BillAddr?.Line1 ?? null,
        city: response.BillAddr?.City ?? null,
        postalCode: response.BillAddr?.PostalCode ?? null,
        region: response.BillAddr?.CountrySubDivisionCode ?? null,
        country: response.BillAddr?.Country ?? null,
      },
    ],
    isActive: response.Active, // Changed from 'status' to 'isActive'
    balance: response.Balance, // This matches the Prisma schema type
    taxNumber: response.TaxIdentifier ?? null,
    createdAtPlatform: response.MetaData?.CreateTime
      ? new Date(response.MetaData.CreateTime)
      : new Date(),
    updatedAtPlatform: response.MetaData?.LastUpdatedTime
      ? new Date(response.MetaData.LastUpdatedTime)
      : null, // Changed to allow null to match Prisma schema
    domain: response.domain,
  };
};

/**
 * Map QBO Vendor response to unified format and save to database
 * @deprecated Use VendorRepository.save() instead
 */
export const mapAndSaveVendor = async (
  qboVendor: any,
  connectionId: string
): Promise<any> => {
  // Import here to avoid circular dependencies
  const { VendorRepository } = await import("../repositories");

  // Use existing mapper to convert QBO response to unified format
  const unifiedVendor = mapQboVendorResponseToUnified(qboVendor);

  // Use repository to save
  return await VendorRepository.save(unifiedVendor, connectionId);
};
