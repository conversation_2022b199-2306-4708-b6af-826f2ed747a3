services:
  app:
    build: .
    container_name: zact-unified-backend
    restart: unless-stopped
    ports:
      - "${PORT:-8080}:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DATABASE_URL=${DATABASE_URL}
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=${KAFKA_CLIENT_ID}
      - QBO_CLIENT_ID=${QBO_CLIENT_ID}
      - QBO_CLIENT_SECRET=${QBO_CLIENT_SECRET}
      - API_GATEWAY_URL=${API_GATEWAY_URL}
      - JWT_SECRET=${JWT_SECRET}
      - INCREMENTAL_SYNC_CRON_ENABLED=${INCREMENTAL_SYNC_CRON_ENABLED:-false}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - mysql
      - kafka

  mysql:
    image: mysql:8.0
    container_name: zact-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-zact_unified}
      MYSQL_USER: ${MYSQL_USER:-zact_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-zact_password}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    volumes:
      - kafka_data:/var/lib/kafka/data

volumes:
  mysql_data:
  kafka_data:
