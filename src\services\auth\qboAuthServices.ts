import { ConnectionStatus, SyncOperationType } from "@prisma/client";
import { envConfig } from "../../config/config";
import prisma from "../../config/db";
import ApiException from "../../utils/api-exception";
import logger from "../../utils/logger";
import { ErrorCode, HttpStatus } from "../../utils/response";
import {
  createAuthData,
  exchangeCodeForToken,
  checkExistingIntegrations,
  upsertIntegration,
} from "../../utils/auth-utils";
import {
  startQboBackgroundSync,
  fetchQboCompanyInfo,
  handleQboInactiveIntegrationReconnection,
} from "../../utils/qbo-utils";

const getQboAuthUrl = (orgId: string) => {
  const { clientId, redirectUri, authUrl } = envConfig.qbo;

  const scopes = [
    "com.intuit.quickbooks.accounting",
    "openid",
    "profile",
    "email",
  ].join(" ");

  const params = new URLSearchParams({
    client_id: clientId,
    response_type: "code",
    scope: scopes,
    redirect_uri: redirectUri,
    state: orgId, // Using orgId as state for simplicity
  });

  const genratedAuthUrl = `${authUrl}?${params.toString()}`;
  //do res.send on generated url
  return {
    data: genratedAuthUrl,
    message: "QBO authorization URL generated successfully",
  };
};

/**
 * Handles the OAuth callback from QBO
 * @param code - The authorization code received from QBO
 * @returns Access token information including access_token, refresh_token, etc.
 */

/**
 * QBO-specific interface for auth response
 */
interface QboAuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  id_token?: string;
  scope?: string;
}

const handleQboCallback = async (
  code: string,
  realmId: string,
  state: string // orgId is passed as state
) => {
  try {
    const { clientId, clientSecret, redirectUri, tokenUrl, baseUrl } =
      envConfig.qbo;
    const orgId = state; // orgId was passed as state

    // Validate required configuration
    if (!clientId || !clientSecret || !redirectUri || !tokenUrl || !baseUrl) {
      throw new ApiException({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        code: ErrorCode.INTERNAL_ERROR,
        message: "QBO configuration is incomplete",
      });
    }

    // Step 1: Exchange authorization code for access token
    const { access_token, refresh_token, expires_in } =
      await exchangeCodeForToken(
        code,
        clientId,
        clientSecret,
        redirectUri,
        tokenUrl
      );

    // Step 2: Fetch company information
    const companyName = await fetchQboCompanyInfo(
      access_token,
      realmId,
      baseUrl
    );

    // Step 3: Check for existing integrations and handle conflicts
    const { existingIntegration, existingIntegrationWithSameExternalId } =
      await checkExistingIntegrations(orgId, realmId, "qbo");

    // Step 4: Handle case where QBO company is already connected to a different Zact org
    if (
      existingIntegrationWithSameExternalId &&
      existingIntegrationWithSameExternalId.connectionStatus ===
        ConnectionStatus.ACTIVE
    ) {
      throw new ApiException({
        status: HttpStatus.CONFLICT,
        code: ErrorCode.CONFLICT,
        message: `This QuickBooks company is already actively connected to a different organization. Please disconnect from the other organization first.`,
      });
    }

    // Step 5: Handle case where we're reconnecting an inactive integration
    if (
      existingIntegrationWithSameExternalId &&
      existingIntegrationWithSameExternalId.connectionStatus ===
        ConnectionStatus.INACTIVE
    ) {
      return await handleQboInactiveIntegrationReconnection(
        existingIntegrationWithSameExternalId.id,
        orgId,
        access_token,
        refresh_token,
        expires_in,
        realmId,
        companyName
      );
    }

    // Step 6: Determine sync type based on whether this is a new or existing connection
    // or if there are any entity sync states with null lastSuccessfulSyncAt
    let shouldRunInitialSync = !existingIntegration;

    if (!shouldRunInitialSync && existingIntegration) {
      // Check if there are any entity sync states with null lastSuccessfulSyncAt
      const entitySyncStates = await prisma.entitySyncState.findMany({
        where: {
          connectionId: existingIntegration.id,
          lastSuccessfulSyncAt: null,
        },
      });

      // If we found any entity sync states with null lastSuccessfulSyncAt, run a complete sync
      shouldRunInitialSync = entitySyncStates.length > 0;

      if (shouldRunInitialSync) {
        logger.info(
          `Found entity sync states with null lastSuccessfulSyncAt, running complete sync`,
          {
            orgId,
            realmId,
            entitySyncStatesCount: entitySyncStates.length,
          }
        );
      }
    }

    const syncOperationType = shouldRunInitialSync
      ? SyncOperationType.FULL_SYNC
      : SyncOperationType.INCREMENTAL_SYNC;

    logger.info(`Sync operation type: ${syncOperationType}`, {
      shouldRunInitialSync,
      orgId,
      realmId,
    });

    // Step 7: Create or update the integration
    const authData = createAuthData(access_token, refresh_token, expires_in);
    const integration = await upsertIntegration(
      orgId,
      realmId,
      companyName,
      authData,
      "qbo"
    );

    // Step 8: Initiate background sync
    startQboBackgroundSync(
      integration.id,
      access_token,
      realmId,
      syncOperationType
    );

    // Step 9: Return success response
    const message = shouldRunInitialSync
      ? "Successfully connected to QBO and tokens stored. Complete sync started in background."
      : "Successfully connected to QBO and tokens stored. Instant sync started in background for updated data.";

    return {
      message,
      data: {
        connectionId: integration.id,
        realmId: integration.externalCompanyId,
        erpSystem: "qbo",
      },
    };
  } catch (error) {
    logger.error("Error in QBO callback handler", {
      error,
      realmId,
      orgId: state,
    });

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to process QBO callback",
    });
  }
};

/**
 * Disconnects a QBO integration for a given organization
 */
const disconnectQbo = async (zactOrgId: string) => {
  try {
    // Validate zactOrgId
    if (!zactOrgId) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.FORBIDDEN,
        message: "Organization ID is required",
      });
    }

    // Find the integration to disconnect
    const integration = await prisma.accountingPlatformIntegration.findUnique({
      where: {
        zactCompanyId: zactOrgId,
      },
    });

    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        message: "QBO integration not found for the given organization",
      });
    }

    // Update the integration to mark it as disconnected
    await prisma.accountingPlatformIntegration.update({
      where: {
        id: integration.id,
      },
      data: {
        connectionStatus: ConnectionStatus.INACTIVE,
        authentication: {
          accessToken: null,
          refreshToken: null,
          expiresAt: null,
          lastRefreshedAt: null,
        },
      },
    });

    logger.info(
      `Successfully disconnected QBO integration for organization ${zactOrgId}`,
      {
        integrationId: integration.id,
        orgId: zactOrgId,
      }
    );

    return {
      message: "Successfully disconnected from QBO",
    };
  } catch (error) {
    logger.error(
      `Error disconnecting QBO integration for organization ${zactOrgId}`,
      { error }
    );

    if (error instanceof ApiException) {
      throw error;
    }

    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: "Failed to disconnect from QBO",
    });
  }
};

export const qboAuthServices = {
  getQboAuthUrl,
  handleQboCallback,
  disconnectQbo,
};
