# 🐳 Docker Quick Commands

## Development

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Start with tools (Kafka UI, phpMyAdmin)
docker-compose -f docker-compose.dev.yml --profile tools up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f app-dev

# Stop
docker-compose -f docker-compose.dev.yml down
```

## Production

```bash
# Start production environment
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop
docker-compose down
```

## Database Operations

```bash
# Run migrations (development)
docker-compose -f docker-compose.dev.yml exec app-dev npx prisma migrate deploy

# Run migrations (production)
docker-compose exec app npx prisma migrate deploy

# Database backup
docker-compose exec mysql mysqldump -u root -p zact_unified > backup.sql
```

## Build Commands

```bash
# Build production image
docker build -t zact-unified-backend .

# Build development image
docker build -f Dockerfile.dev -t zact-unified-backend:dev .
```

## Access Points

- **App**: http://localhost:8080
- **Kafka UI**: http://localhost:8081 (with --profile tools)
- **phpMyAdmin**: http://localhost:8082 (with --profile tools)
- **Health Check**: http://localhost:8080/health
