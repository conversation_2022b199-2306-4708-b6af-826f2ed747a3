import prisma from "./../../config/db";
import ApiException from "./../../utils/api-exception";
import { ErrorCode, HttpStatus } from "./../../utils/response";
import fetchQBODatafromDB from "./qboService";

const fetchService = async (
  entity: string,
  connectionId: string
): Promise<any> => {
  // This function should implement the logic to fetch services based on the entity and connectionId
  const validateConnection =
    await prisma.accountingPlatformIntegration.findUnique({
      where: {
        id: connectionId,
      },
    });

  if (!validateConnection || !validateConnection.connectionStatus) {
    throw new ApiException({
      status: HttpStatus.UNAUTHORIZED,
      code: ErrorCode.UNAUTHORIZED,
      errorDescription: "Invalid connection ID or connection is not active",
    });
  }

  const accountingPlatformType = validateConnection.accountingPlatformType;

  switch (accountingPlatformType) {
    case "QBO":
      return fetchQBODatafromDB(entity, connectionId);
  }
};

const getSyncHistory = async (
  page: number,
  limit: number,
  zactCompanyId: string
) => {
  // Fetch data from database
  const result = await prisma.accountingPlatformIntegration.findUnique({
    where: {
      zactCompanyId,
    },
    select: {
      syncOperations: {
        take: limit,
        skip: (page - 1) * limit,
        orderBy: {
          syncStartedAt: "desc",
        },
      },
      // You'll need to include company info if it's available in your schema
      companyName: true,
    },
  });

  // Transform the response to match the desired format
  if (!result) {
    return { data: [] };
  }

  const transformedData = result.syncOperations.map((operation) => {
    // Calculate duration in milliseconds
    const duration =
      operation.syncCompletedAt && operation.syncStartedAt
        ? new Date(operation.syncCompletedAt).getTime() -
          new Date(operation.syncStartedAt).getTime()
        : null;

    return {
      id: operation.id,
      entityType: operation.entityType.toLowerCase(), // Convert to lowercase
      status: operation.status,
      startedAt: operation.syncStartedAt,
      completedAt: operation.syncCompletedAt,
      duration: duration,
      companyName: result.companyName || "Unknown Company", // You'll need to adjust this based on your schema
      recordsProcessed: operation.recordsProcessed,
      recordsSucceeded: operation.recordsSucceeded,
      recordsFailed: operation.recordsFailed,
      errorMessage: operation.errorMessage,
    };
  });

  return {
    data: transformedData,
  };
};

export const services = {
  fetchService,
  getSyncHistory,
};
