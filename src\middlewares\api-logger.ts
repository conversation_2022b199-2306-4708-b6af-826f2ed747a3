import { NextFunction, Request, Response } from "express";
import { LogType } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import prisma from "../config/db";

/**
 * Middleware to log API requests to the database RequestLog table
 */
export const apiLogger = (req: Request, res: Response, next: NextFunction) => {
  const originalJson = res.json;
  const startTime = Date.now();

  const connectionId =
    ((req.query.connectionId || req.headers["connectionId"]) as string) || null;

  // Capture only essential request details, excluding headers
  const requestDetails = {
    query: { ...req.query },
    params: { ...req.params },
    ip: req.ip,
    timestamp: new Date().toISOString(),
  };

  let responseBody: any = {};

  // Override res.json to capture response body
  res.json = function (body?: any): any {
    responseBody = body;
    return originalJson.call(this, body);
  };

  // Listen to `finish` event to log after response is sent
  res.on("finish", () => {
    const executionTime = Date.now() - startTime;

    prisma.requestLog
      .create({
        data: {
          sourceId: uuidv4(),
          connectionId,
          logType: LogType.API,
          endpointOrTopic: req.originalUrl,
          methodOrAction: req.method,
          executionTime: new Date(),
          details: {
            request: requestDetails,
            response: {
              responseStatus: res.statusCode,
              message: responseBody?.message || "",
              executionTimeMs: executionTime,
            },
          },
          status: res.statusCode,
          message: res.statusMessage,
        },
      })
      .catch((error) => {
        console.error("Error logging API request to database:", error);
      });
  });

  next();
};

export default apiLogger;
