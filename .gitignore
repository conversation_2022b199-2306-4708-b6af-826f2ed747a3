# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist
build
out
.next

# Logs
logs
*.log

# Coverage directory used by tools like istanbul
coverage

# Prisma
/src/generated/prisma

# IDE specific files
.idea
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace
.history/

# OS specific files
.DS_Store
Thumbs.db
