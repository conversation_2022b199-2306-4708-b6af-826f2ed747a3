# Zact Unified Backend - Complete Development Environment Setup
# This script sets up the complete local development environment

param(
    [Parameter(Mandatory=$false)]
    [switch]$SkipInfrastructure,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDatabase,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDependencies
)

$ErrorActionPreference = "Stop"

# Colors for output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }
function Write-Header { Write-ColorOutput Magenta $args }

# Check prerequisites
function Test-Prerequisites {
    Write-Info "🔍 Checking prerequisites..."
    
    $missing = @()
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Success "✅ Node.js: $nodeVersion"
    } catch {
        $missing += "Node.js"
    }
    
    # Check npm
    try {
        $npmVersion = npm --version
        Write-Success "✅ npm: $npmVersion"
    } catch {
        $missing += "npm"
    }
    
    # Check Docker
    try {
        docker --version | Out-Null
        Write-Success "✅ Docker: Available"
    } catch {
        $missing += "Docker"
    }
    
    # Check MySQL (optional check)
    try {
        mysql --version | Out-Null
        Write-Success "✅ MySQL: Available"
    } catch {
        Write-Warning "⚠️  MySQL CLI not found (this is optional)"
    }
    
    if ($missing.Count -gt 0) {
        Write-Error "❌ Missing prerequisites: $($missing -join ', ')"
        Write-Error "Please install the missing tools and try again."
        exit 1
    }
    
    Write-Success "✅ All prerequisites are available"
}

# Install dependencies
function Install-Dependencies {
    if ($SkipDependencies) {
        Write-Info "⏭️  Skipping dependency installation"
        return
    }
    
    Write-Info "📦 Installing Node.js dependencies..."
    
    if (Test-Path "package-lock.json") {
        npm ci
    } else {
        npm install
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✅ Dependencies installed successfully"
    } else {
        Write-Error "❌ Failed to install dependencies"
        exit 1
    }
}

# Setup infrastructure
function Setup-Infrastructure {
    if ($SkipInfrastructure) {
        Write-Info "⏭️  Skipping infrastructure setup"
        return
    }
    
    Write-Info "🏗️  Setting up Kafka infrastructure..."
    
    if (Test-Path "scripts/setup-local.ps1") {
        & "scripts/setup-local.ps1" -Action start
    } else {
        Write-Error "❌ Infrastructure setup script not found"
        exit 1
    }
}

# Setup database
function Setup-Database {
    if ($SkipDatabase) {
        Write-Info "⏭️  Skipping database setup"
        return
    }
    
    Write-Info "🗄️  Setting up database..."
    
    if (Test-Path "scripts/setup-database.js") {
        node "scripts/setup-database.js"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Database setup completed"
        } else {
            Write-Error "❌ Database setup failed"
            exit 1
        }
    } else {
        Write-Error "❌ Database setup script not found"
        exit 1
    }
}

# Create Kafka topics
function Create-KafkaTopics {
    Write-Info "📝 Creating Kafka topics..."
    
    $topics = @(
        "entity-create-request",
        "entity-create-response", 
        "entity-batch-stream",
        "sync-completion-response"
    )
    
    foreach ($topic in $topics) {
        Write-Info "Creating topic: $topic"
        try {
            docker exec zact-kafka-local kafka-topics --create --topic $topic --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists
            Write-Success "✅ Topic '$topic' created/verified"
        } catch {
            Write-Warning "⚠️  Failed to create topic '$topic' (it may already exist)"
        }
    }
}

# Verify setup
function Test-Setup {
    Write-Info "🧪 Verifying setup..."
    
    # Test database connection
    Write-Info "Testing database connection..."
    if (Test-Path "test-mysql-connection.js") {
        node "test-mysql-connection.js"
    }
    
    # Test Kafka connection
    Write-Info "Testing Kafka connection..."
    try {
        docker exec zact-kafka-local kafka-broker-api-versions --bootstrap-server localhost:9092 | Out-Null
        Write-Success "✅ Kafka connection successful"
    } catch {
        Write-Warning "⚠️  Kafka connection test failed"
    }
    
    # Check if all services are running
    Write-Info "Checking service status..."
    & "scripts/setup-local.ps1" -Action status
}

# Show next steps
function Show-NextSteps {
    Write-Header ""
    Write-Header "🎉 Development Environment Setup Complete!"
    Write-Header "========================================"
    Write-Info ""
    Write-Info "🚀 Next Steps:"
    Write-Info "1. Start the development server:"
    Write-Info "   npm run dev"
    Write-Info ""
    Write-Info "2. Access the services:"
    Write-Info "   • Backend API: http://localhost:8080"
    Write-Info "   • Kafka UI: http://localhost:8090"
    Write-Info ""
    Write-Info "3. Useful commands:"
    Write-Info "   • View infrastructure status: scripts/setup-local.ps1 status"
    Write-Info "   • View logs: scripts/setup-local.ps1 logs"
    Write-Info "   • Stop infrastructure: scripts/setup-local.ps1 stop"
    Write-Info "   • Run tests: npm test"
    Write-Info ""
    Write-Info "4. Environment files:"
    Write-Info "   • Main config: .env"
    Write-Info "   • Database: Already configured for local MySQL"
    Write-Info "   • Kafka: localhost:9092"
    Write-Info ""
    Write-Success "✅ Happy coding! 🎯"
}

# Main execution
Write-Header "🏗️  Zact Unified Backend - Development Environment Setup"
Write-Header "======================================================="

try {
    Test-Prerequisites
    Install-Dependencies
    Setup-Infrastructure
    Start-Sleep -Seconds 5  # Wait for infrastructure to be ready
    Create-KafkaTopics
    Setup-Database
    Test-Setup
    Show-NextSteps
} catch {
    Write-Error "❌ Setup failed: $($_.Exception.Message)"
    Write-Error "Please check the error above and try again."
    exit 1
}
