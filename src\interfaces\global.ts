import { Request } from "express";

export interface RequestExtended extends Request {
  user?: any;
  id?: any;
  traceId?: any;
  file?: any;
  logId?: any;
  log?: any;
  error?: any;
  idAdmin?: any;
  accessToken?: any;
  refreshToken?: any;
  messageId?: any;
}

export interface DefaultResponseInterface {
  message: string;
  statusCode: number;
  data: any;
  total?: number;
  page?: number;
}

export interface IUnifiedAccount {
  id: string;
  fullyQualifiedName: string; // MANDATORY - Required by ALL platforms
  name: string; // MANDATORY - Required by ALL platforms
  accountType: string; // MANDATORY - Required by ALL platforms
  accountSubType: string | null;
  category: string | null;
  parentId: string | null;
  active: boolean; // MANDATORY - Required by ALL platforms
  currentBalance: number; // MANDATORY - Required by ALL platforms
  subAccount: boolean;
  currentBalanceWithSubAccounts: number | null;
  currencyCode: string | null;
  // Optional fields - Platform specific requirements
  accountNumber?: string | null; // Required by: <PERSON><PERSON>, <PERSON>ero, QB<PERSON>, <PERSON>Suite, Merge, Rutter (not universal)
  description?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  bankAccountNumber?: string | null; // Required by: QBO, Xero, NetSuite only (for bank accounts)
  routingNumber?: string | null; // Required by: QBO, NetSuite only (for bank accounts)
  taxType?: string | null; // Required by: Xero, NetSuite only (for tax accounts)
  // Sync tracking fields
  externalId?: string | null; // For platform-specific IDs
  lastSyncedAt?: Date | null; // For sync tracking
  platformUrl?: string | null; // For deep linking
  domain: string;
  createdDate: Date;
  updatedDate: Date;
}
