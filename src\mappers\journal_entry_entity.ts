import { IQboJournalEntryResponse, IUnifiedJournalEntry } from "../interfaces";

export const mapQboJournalEntryToUnified = (
  qboEntry: IQboJournalEntryResponse
): IUnifiedJournalEntry => {
  return {
    id: qboEntry.Id,
    domain: qboEntry.domain,
    transactionDate: qboEntry.TxnDate,
    totalAmount: qboEntry.TotalAmt,
    currencyCode: qboEntry.CurrencyRef?.value ?? null,
    memo: qboEntry.PrivateNote ?? null, // MANDATORY field - map from QBO PrivateNote
    lineItems: qboEntry.Line.map((line) => ({
      id: line?.Id,
      description: line.Description ?? "",
      totalAmount: line.Amount,
      postingType: line.JournalEntryLineDetail?.PostingType ?? "Debit", // Default to "Debit" if PostingType is undefined
      detailType: line.DetailType,
      accountId: line.JournalEntryLineDetail?.AccountRef?.value ?? "",
    })),
    createdAtPlatform: qboEntry.MetaData?.CreateTime
      ? new Date(qboEntry.MetaData.CreateTime)
      : new Date(),
    updatedAtPlatform: qboEntry.MetaData?.LastUpdatedTime
      ? new Date(qboEntry.MetaData.LastUpdatedTime)
      : new Date(),
  };
};

export const mapUnifiedToQboJournalEntryRequest = (
  journalEntry: Partial<IUnifiedJournalEntry>
): Partial<IQboJournalEntryResponse> => {
  return {
    TxnDate:
      journalEntry.transactionDate ?? new Date().toISOString().split("T")[0],
    TotalAmt: journalEntry.totalAmount,
    CurrencyRef: journalEntry.currencyCode
      ? { value: journalEntry.currencyCode }
      : undefined,
    PrivateNote: journalEntry.memo ?? undefined, // Map memo to QBO PrivateNote
    Line:
      journalEntry.lineItems?.map((line) => ({
        Amount: line.totalAmount,
        Description: line.description,
        DetailType: line.detailType ?? "JournalEntryLineDetail",
        JournalEntryLineDetail: {
          PostingType: line.postingType,
          AccountRef: {
            value: line.accountId,
          },
        },
      })) ?? [],
  };
};

/**
 * Map QBO JournalEntry response to unified format and save to database
 * @deprecated Use JournalEntryRepository.save() instead
 */
export const mapAndSaveJournalEntry = async (
  qboJournalEntry: any,
  connectionId: string
): Promise<any> => {
  // Import here to avoid circular dependencies
  const { JournalEntryRepository } = await import("../repositories");

  // Use existing mapper to convert QBO response to unified format
  const unifiedJournalEntry = mapQboJournalEntryToUnified(qboJournalEntry);

  // Use repository to save
  return await JournalEntryRepository.save(unifiedJournalEntry, connectionId);
};
