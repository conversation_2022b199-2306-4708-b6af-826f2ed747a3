{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Kafka Message Schema", "description": "Complete schema for Kafka messages sent to entity-create-request topic", "type": "object", "required": ["messageId", "correlationId", "timestamp", "source", "destination", "messageType", "erpSystem", "entityOperation", "securityContext", "payload"], "properties": {"messageId": {"type": "string", "description": "Unique identifier for this message (UUID)"}, "correlationId": {"type": "string", "description": "Correlation ID to track related messages (UUID)"}, "timestamp": {"type": "number", "description": "Unix timestamp when message was created"}, "source": {"type": "string", "enum": ["ZACT_APP", "UNIFIED_BACKEND", "ERP_SERVICE"], "description": "Source system that created the message"}, "destination": {"type": "string", "enum": ["ZACT_APP", "UNIFIED_BACKEND", "ERP_SERVICE"], "description": "Destination system for the message"}, "messageType": {"type": "string", "enum": ["REQUEST", "RESPONSE", "EVENT"], "description": "Type of message"}, "erpSystem": {"type": "string", "enum": ["QBO", "XERO", "NETSUITE"], "description": "Target ERP system"}, "entityOperation": {"type": "object", "required": ["operation"], "properties": {"entityType": {"type": "string", "enum": ["account", "vendor", "class", "bill", "payment", "journal_entry", "all"], "description": "Type of entity being operated on"}, "operation": {"type": "string", "enum": ["FETCH", "CREATE", "UPDATE", "DELETE", "SYNC"], "description": "Operation being performed"}}}, "filters": {"type": ["object", "null"], "description": "Optional filters for the operation"}, "metadata": {"type": ["object", "null"], "properties": {"processingTimeMs": {"type": ["number", "null"], "description": "Processing time in milliseconds"}, "errorType": {"type": ["string", "null"], "description": "Error type if applicable"}, "errorDetails": {"type": ["string", "null"], "description": "Error details if applicable"}}}, "securityContext": {"type": "object", "required": ["connectionId"], "properties": {"connectionId": {"type": "string", "description": "Connection ID for the accounting platform integration"}}}, "payload": {"type": "array", "description": "Array of unified entity objects to be created", "items": {"oneOf": [{"$ref": "#/definitions/UnifiedAccount"}, {"$ref": "#/definitions/UnifiedVendor"}, {"$ref": "#/definitions/UnifiedClass"}, {"$ref": "#/definitions/UnifiedBill"}, {"$ref": "#/definitions/UnifiedPayment"}, {"$ref": "#/definitions/UnifiedJournalEntry"}]}}, "status": {"type": ["null"], "description": "Status is null for request messages"}}, "definitions": {"UnifiedAccount": {"type": "object", "description": "See account-schema.json for complete schema"}, "UnifiedVendor": {"type": "object", "description": "See vendor-schema.json for complete schema"}, "UnifiedClass": {"type": "object", "description": "See class-schema.json for complete schema"}, "UnifiedBill": {"type": "object", "description": "See bill-schema.json for complete schema"}, "UnifiedPayment": {"type": "object", "description": "See payment-schema.json for complete schema"}, "UnifiedJournalEntry": {"type": "object", "description": "See journal-entry-schema.json for complete schema"}}, "examples": [{"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "ZACT_APP", "destination": "UNIFIED_BACKEND", "messageType": "REQUEST", "erpSystem": "QBO", "entityOperation": {"entityType": "vendor", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-123-456-789"}, "payload": [{"vendorName": "ABC Office Supplies", "contactName": "<PERSON>", "email": "<EMAIL>", "phone": "******-123-4567", "website": "https://www.abcoffice.com", "isActive": true, "domain": "qbo"}], "status": null}], "syncExamples": [{"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "account", "operation": "FETCH"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-123-456-789"}, "payload": {"data": [{"id": "db-uuid-account-1", "externalId": "1", "fullyQualifiedName": "Assets:Current Assets:Checking Account", "name": "Checking Account", "accountType": "Bank", "active": true, "currentBalance": 15000.5, "subAccount": false, "domain": "qbo"}], "entityType": "account", "batchNumber": 1, "totalBatches": 1, "batchSize": 1000}, "status": {"code": "200", "message": "Data batch 1/1 for account"}}], "syncCompletionExamples": [{"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "ALL", "operation": "SYNC"}, "filters": null, "metadata": {"processingTimeMs": 45000, "errorType": null, "errorDetails": null}, "securityContext": {"zactCompanyId": "company-uuid-123"}, "payload": {"message": "Full sync finished successfully", "entityCounts": {"account": 25, "vendor": 12, "class": 8}, "syncType": "FULL_SYNC", "startTime": "2024-01-15T10:00:00Z", "endTime": "2024-01-15T10:45:00Z", "totalRecords": 128}, "status": {"code": "200", "message": "Full sync completed successfully"}}, {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "ALL", "operation": "SYNC"}, "filters": null, "metadata": {"processingTimeMs": 15000, "errorType": null, "errorDetails": null}, "securityContext": {"zactCompanyId": "company-uuid-456"}, "payload": {"message": "Incremental sync finished successfully", "entityCounts": {"account": 3, "vendor": 2, "class": 1}, "syncType": "INCREMENTAL_SYNC", "startTime": "2024-01-15T11:00:00Z", "endTime": "2024-01-15T11:15:00Z", "totalRecords": 6}, "status": {"code": "200", "message": "Incremental sync completed successfully"}}, {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "ALL", "operation": "SYNC"}, "filters": null, "metadata": {"processingTimeMs": 25000, "errorType": null, "errorDetails": null}, "securityContext": {"zactCompanyId": "company-uuid-789"}, "payload": {"message": "Scheduled sync finished successfully", "entityCounts": {"account": 8, "vendor": 5, "class": 3}, "syncType": "SCHEDULED_SYNC", "startTime": "2024-01-15T12:00:00Z", "endTime": "2024-01-15T12:25:00Z", "totalRecords": 16}, "status": {"code": "200", "message": "Scheduled sync completed successfully"}}]}