export interface IQboAccountResponse {
  Id: string;
  FullyQualifiedName: string;
  AccountType: string;
  AccountSubType?: string;
  Name: string;
  Classification?: string;
  ParentRef?: {
    value: string;
  };
  Active: boolean;
  CurrentBalance: number;
  SubAccount: boolean;
  CurrentBalanceWithSubAccounts: number;
  CurrencyRef?: {
    value: string;
    name?: string;
  };
  domain: string;
  MetaData?: {
    CreateTime?: string;
    LastUpdatedTime?: string;
  };
}

export interface IUnifiedAccount {
  id: string;
  fullyQualifiedName: string; // MANDATORY - Required by ALL platforms
  name: string; // MANDATORY - Required by ALL platforms
  accountType: string; // MANDATORY - Required by ALL platforms
  accountSubType: string | null;
  category: string | null;
  parentId: string | null;
  active: boolean; // MANDATORY - Required by ALL platforms
  currentBalance: number; // MANDATORY - Required by ALL platforms
  subAccount: boolean;
  currentBalanceWithSubAccounts: number | null;
  currencyCode: string | null;
  // Optional fields - Platform specific requirements
  accountNumber?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  description?: string | null; // Required by: QBO, Xero, QBD, NetSuite, Merge, Rutter (not universal)
  bankAccountNumber?: string | null; // Required by: QBO, Xero, NetSuite only (for bank accounts)
  routingNumber?: string | null; // Required by: QBO, NetSuite only (for bank accounts)
  taxType?: string | null; // Required by: Xero, NetSuite only (for tax accounts)
  // Sync tracking fields
  externalId?: string | null; // For platform-specific IDs
  lastSyncedAt?: Date | null; // For sync tracking
  platformUrl?: string | null; // For deep linking
  domain: string;
  createdAtPlatform: Date;
  updatedAtPlatform: Date;
}

export const mapQboAccountResponseToUnified = (
  response: IQboAccountResponse
): IUnifiedAccount => {
  return {
    id: response.Id,
    fullyQualifiedName: response.FullyQualifiedName,
    name: response.Name,
    accountType: response.AccountType,
    accountSubType: response.AccountSubType ?? null,
    category: response.Classification ?? null,
    parentId: response.ParentRef?.value ?? null,
    active: response.Active,
    currentBalance: response.CurrentBalance,
    subAccount: response.SubAccount,
    currentBalanceWithSubAccounts:
      response.CurrentBalanceWithSubAccounts ?? null,
    currencyCode: response.CurrencyRef?.value ?? null,
    domain: response.domain,
    createdAtPlatform: response.MetaData?.CreateTime
      ? new Date(response.MetaData.CreateTime)
      : new Date(),
    updatedAtPlatform: response.MetaData?.LastUpdatedTime
      ? new Date(response.MetaData.LastUpdatedTime)
      : new Date(),
  };
};

export const mapUnifiedToQboAccountResponse = (
  account: IUnifiedAccount
): IQboAccountResponse => {
  return {
    Id: account.id,
    FullyQualifiedName: account.fullyQualifiedName || account.name,
    Name: account.name || account.fullyQualifiedName,
    AccountType: account.accountType,
    AccountSubType: account.accountSubType ?? "",
    Classification: account.category ?? "",
    ParentRef: account.parentId ? { value: account.parentId } : { value: "" },
    Active: account.active,
    CurrentBalance: account.currentBalance,
    SubAccount: account.subAccount,
    CurrentBalanceWithSubAccounts: account.currentBalanceWithSubAccounts ?? 0,
    CurrencyRef: account.currencyCode
      ? { value: account.currencyCode }
      : { value: "" },
    domain: account.domain,
  };
};
