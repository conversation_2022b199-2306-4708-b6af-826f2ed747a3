import express from "express";
import asyncHandler from "../utils/async-handler";
import ApiException from "../utils/api-exception";
import { ErrorCodes } from "../utils/response";
import { authServices } from "../services/auth";
const router = express.Router();
// Route to initiate QBO OAuth flow
router.get(
  "/connect/:accountingPlatform",
  asyncHandler(async (req, res) => {
    if (!req.params.accountingPlatform) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: "Accounting Platform type parameter is required",
      });
    }

    if (req.params.accountingPlatform !== "qbo") {
      throw new ApiException({
        ...ErrorCodes.UNPROCESSABLE_ENTITY,
        errorDescription:
          "Unsupported accounting Platform type. Only 'qbo' is supported",
      });
    }

    switch (req.params.accountingPlatform) {
      case "qbo":
        if (!req.headers.zactcompanyid) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription: "zactcompanyid header is required",
          });
        }
        const authUrl = authServices.qboAuthServices.getQboAuthUrl(
          req.headers.zactcompanyid as string
        );
        res.json({ authUri: authUrl.data });
      default:
        return res.status(400).send("Unsupported accounting Platform type");
    }
  })
);
// Route to handle QBO OAuth callback
router.get(
  "/:accountingPlatform/callback",
  asyncHandler(async (req, res) => {
    switch (req.params.accountingPlatform) {
      case "qbo":
        const { code, realmId, state } = req.query;

        if (!code || !realmId || !state) {
          throw new ApiException({
            ...ErrorCodes.BAD_REQUEST,
            errorDescription:
              "code, realmId, and state parameters are required",
          });
        }
        const callBackResponse =
          await authServices.qboAuthServices.handleQboCallback(
            code as string,
            realmId as string,
            state as string
          );
        res.redirect(
          `http://localhost:4200?erpSystem=${callBackResponse.data.erpSystem}&realmId=${callBackResponse.data.realmId}`
        );
        break;
      default:
        res.status(400).send("Unsupported accounting Platform type");
    }
  })
);

router.post(
  "/disconnect",
  asyncHandler(async (req, res) => {
    const { zactcompanyid } = req.headers;

    if (!zactcompanyid) {
      throw new ApiException({
        ...ErrorCodes.BAD_REQUEST,
        errorDescription: "zactcompanyid header is required",
      });
    }

    const disconnectResponse = await authServices.qboAuthServices.disconnectQbo(
      zactcompanyid as string
    );
    return disconnectResponse;
  })
);

export default router;
