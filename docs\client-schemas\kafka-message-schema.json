{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Kafka Message Schema", "description": "Complete schema for Kafka messages sent to entity-create-request topic", "type": "object", "required": ["messageId", "correlationId", "timestamp", "source", "destination", "messageType", "erpSystem", "entityOperation", "securityContext", "payload"], "properties": {"messageId": {"type": "string", "description": "Unique identifier for this message (UUID)"}, "correlationId": {"type": "string", "description": "Correlation ID to track related messages (UUID)"}, "timestamp": {"type": "number", "description": "Unix timestamp when message was created"}, "source": {"type": "string", "enum": ["ZACT_APP", "UNIFIED_BACKEND", "ERP_SERVICE"], "description": "Source system that created the message"}, "destination": {"type": "string", "enum": ["ZACT_APP", "UNIFIED_BACKEND", "ERP_SERVICE"], "description": "Destination system for the message"}, "messageType": {"type": "string", "enum": ["REQUEST", "RESPONSE", "EVENT"], "description": "Type of message"}, "erpSystem": {"type": "string", "enum": ["QBO", "XERO", "NETSUITE"], "description": "Target ERP system"}, "entityOperation": {"type": "object", "required": ["operation"], "properties": {"entityType": {"type": "string", "enum": ["account", "vendor", "class", "bill", "payment", "journal_entry", "all"], "description": "Type of entity being operated on"}, "operation": {"type": "string", "enum": ["FETCH", "CREATE", "UPDATE", "DELETE", "SYNC"], "description": "Operation being performed"}}}, "filters": {"type": ["object", "null"], "description": "Optional filters for the operation"}, "metadata": {"type": ["object", "null"], "properties": {"processingTimeMs": {"type": ["number", "null"], "description": "Processing time in milliseconds"}, "errorType": {"type": ["string", "null"], "description": "Error type if applicable"}, "errorDetails": {"type": ["string", "null"], "description": "Error details if applicable"}}}, "securityContext": {"type": "object", "required": ["connectionId"], "properties": {"connectionId": {"type": "string", "description": "Connection ID for the accounting platform integration"}}}, "payload": {"type": "array", "description": "Array of unified entity objects to be created", "items": {"oneOf": [{"$ref": "#/definitions/UnifiedAccount"}, {"$ref": "#/definitions/UnifiedVendor"}, {"$ref": "#/definitions/UnifiedClass"}, {"$ref": "#/definitions/UnifiedBill"}, {"$ref": "#/definitions/UnifiedPayment"}, {"$ref": "#/definitions/UnifiedJournalEntry"}]}}, "status": {"type": ["null"], "description": "Status is null for request messages"}}, "definitions": {"UnifiedAccount": {"type": "object", "description": "See account-schema.json for complete schema"}, "UnifiedVendor": {"type": "object", "description": "See vendor-schema.json for complete schema"}, "UnifiedClass": {"type": "object", "description": "See class-schema.json for complete schema"}, "UnifiedBill": {"type": "object", "description": "See bill-schema.json for complete schema"}, "UnifiedPayment": {"type": "object", "description": "See payment-schema.json for complete schema"}, "UnifiedJournalEntry": {"type": "object", "description": "See journal-entry-schema.json for complete schema"}}, "examples": [{"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": 17***********, "source": "ZACT_APP", "destination": "UNIFIED_BACKEND", "messageType": "REQUEST", "erpSystem": "QBO", "entityOperation": {"entityType": "vendor", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": [{"id": "123", "vendorName": "ABC Office Supplies", "contactName": "<PERSON>", "email": "<EMAIL>", "phone": "******-123-4567", "website": "https://www.abcoffice.com", "bankAccountNumber": null, "addresses": [{"address1": "123 Business St", "city": "New York", "postalCode": "10001", "region": "NY", "country": "USA"}], "isActive": true, "balance": 2500.75, "taxNumber": "12-3456789", "currencyCode": "USD", "domain": "qbo"}], "status": null}]}