import {
  Kafka,
  Consumer,
  Producer,
  KafkaConfig,
  ProducerConfig,
  ConsumerConfig,
} from "kafkajs";
import logger from "../../utils/logger";
import { envConfig } from "../../config/config";

// Kafka configuration
const kafkaConfig: KafkaConfig = {
  clientId: envConfig.kafka.clientId,
  brokers: envConfig.kafka.brokers,
  ssl: envConfig.kafka.ssl,
  sasl: envConfig.kafka.sasl.enabled
    ? {
        mechanism: envConfig.kafka.sasl.mechanism as any,
        username: envConfig.kafka.sasl.username,
        password: envConfig.kafka.sasl.password,
      }
    : undefined,
  connectionTimeout: envConfig.kafka.connectionTimeout,
  requestTimeout: envConfig.kafka.requestTimeout,
};

// Create Kafka instance
const kafka = new Kafka(kafkaConfig);

// Producer configuration
const producerConfig: ProducerConfig = {
  allowAutoTopicCreation: true,
  transactionTimeout: 30000,
};

// Consumer configuration
const consumerConfig: ConsumerConfig = {
  groupId: envConfig.kafka.consumerGroupId,
  sessionTimeout: 30000,
  heartbeatInterval: 3000,
  maxBytesPerPartition: 1048576, // 1MB
  maxWaitTimeInMs: 5000,
};

// Create producer instance
const createProducer = async (): Promise<Producer> => {
  const producer = kafka.producer(producerConfig);
  try {
    await producer.connect();
    return producer;
  } catch (error) {
    logger.error(`❌ Kafka producer connection failed: ${error}`);
    throw error;
  }
};

// Create consumer instance
const createConsumer = async (): Promise<Consumer> => {
  const consumer = kafka.consumer(consumerConfig);
  try {
    await consumer.connect();
    return consumer;
  } catch (error) {
    logger.error(`❌ Kafka consumer connection failed: ${error}`);
    throw error;
  }
};

export { kafka, createProducer, createConsumer };
