import prisma from "../config/db";
import axios from "axios";
import { envConfig } from "../config/config";
import ApiException from "../utils/api-exception";
import { ErrorCode, HttpStatus } from "../utils/response";
import logger from "../utils/logger";

// In-memory lock for token refresh operations
// Key: connectionId, Value: Promise of the refresh operation
const tokenRefreshLocks = new Map<
  string,
  Promise<{ accessToken: string; realmId: string }>
>();

// Lock timeout (10 seconds)
const LOCK_TIMEOUT = 10000;

/**
 * Function to validate and refresh QBO access token if needed
 * This function:
 * 1. Retrieves the token for the given integration ID
 * 2. Checks if the token is valid and not expired
 * 3. Refreshes the token if it's expired
 * 4. Returns the access token and realm ID
 *
 * This implementation includes a locking mechanism to prevent race conditions
 * when multiple requests attempt to refresh the same token simultaneously.
 *
 * @param connectionId The accounting Platform integration ID
 * @returns Object containing accessToken and realmId
 */
export const getValidQboToken = async (
  connectionId: string
): Promise<{ accessToken: string; realmId: string }> => {
  try {
    // Validate the integration ID
    if (!connectionId) {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        message: "Integration ID is required",
      });
    }

    // Get the accounting Platform integration
    const integration = await prisma.accountingPlatformIntegration.findUnique({
      where: {
        id: connectionId,
      },
    });

    // If no integration found, throw an error
    if (!integration) {
      throw new ApiException({
        status: HttpStatus.NOT_FOUND,
        code: ErrorCode.NOT_FOUND,
        message: `No accounting Platform integration found with ID: ${connectionId}`,
      });
    }

    // If integration is not active, throw an error
    if (integration.connectionStatus !== "ACTIVE") {
      throw new ApiException({
        status: HttpStatus.BAD_REQUEST,
        code: ErrorCode.BAD_REQUEST,
        message: `Integration is not active. Current status: ${integration.connectionStatus}`,
      });
    }

    // Get the realm ID (external company ID)
    const realmId = integration.externalCompanyId;

    // Get the authentication tokens from the integration
    const auth = integration.authentication as any;

    // If no authentication found, throw an error
    if (!auth || !auth.accessToken || !auth.refreshToken || !auth.expiresAt) {
      throw new ApiException({
        status: HttpStatus.UNAUTHORIZED,
        code: ErrorCode.UNAUTHORIZED,
        message: "QBO authentication tokens not found",
      }); 
    }

    // Check if token is expired or about to expire (within 5 minutes)
    const now = new Date();
    const tokenExpiresAt = new Date(auth.expiresAt);
    const thirtyMinutesFromNow = new Date(now.getTime() + 30 * 60 * 1000);

    // If token is valid and not about to expire, return it
    if (tokenExpiresAt > thirtyMinutesFromNow) {
      return {
        accessToken: auth.accessToken,
        realmId,
      };
    }

    // Token is expired or about to expire, check if there's already a refresh in progress
    if (tokenRefreshLocks.has(connectionId)) {
      logger.info(
        `Token refresh already in progress for integration ${connectionId}, waiting for it to complete`
      );
      try {
        // Wait for the existing refresh operation to complete
        return await tokenRefreshLocks.get(connectionId)!;
      } catch (error) {
        // If the existing refresh operation failed, we'll try again
        logger.warn(
          `Previous token refresh for integration ${connectionId} failed, attempting again`
        );
        tokenRefreshLocks.delete(connectionId);
      }
    }

    // Create a new refresh operation and store its promise in the locks map
    const refreshOperation = refreshQboToken(connectionId, auth, realmId);
    tokenRefreshLocks.set(connectionId, refreshOperation);

    // Set a timeout to remove the lock after LOCK_TIMEOUT milliseconds
    // This prevents a deadlock if the refresh operation hangs
    setTimeout(() => {
      if (tokenRefreshLocks.has(connectionId)) {
        logger.warn(
          `Token refresh lock for integration ${connectionId} timed out after ${LOCK_TIMEOUT}ms`
        );
        tokenRefreshLocks.delete(connectionId);
      }
    }, LOCK_TIMEOUT);

    try {
      // Execute the refresh operation
      const result = await refreshOperation;
      // Remove the lock after successful completion
      tokenRefreshLocks.delete(connectionId);
      return result;
    } catch (error) {
      // Remove the lock if the operation fails
      tokenRefreshLocks.delete(connectionId);
      throw error;
    }
  } catch (error) {
    // If it's already an ApiException, just rethrow it
    if (error instanceof ApiException) {
      throw error;
    }

    // For any other type of error
    throw new ApiException({
      status: HttpStatus.INTERNAL_SERVER_ERROR,
      code: ErrorCode.INTERNAL_ERROR,
      message: `Error validating QBO token: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    });
  }
};

/**
 * Helper function to refresh a QBO token
 * This is extracted to a separate function to make the locking mechanism cleaner
 *
 * @param connectionId The accounting Platform integration ID
 * @param auth The current authentication information
 * @param realmId The QBO realm ID
 * @returns Object containing the new accessToken and realmId
 */
async function refreshQboToken(
  connectionId: string,
  auth: any,
  realmId: string
): Promise<{ accessToken: string; realmId: string }> {
  logger.info(`Refreshing QBO token for integration ${connectionId}`);
  const now = new Date();

  try {
    // Prepare refresh token request
    const params = new URLSearchParams({
      grant_type: "refresh_token",
      refresh_token: auth.refreshToken,
    });

    // Make the refresh token request
    const response = await axios.post(envConfig.qbo.tokenUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(
          `${envConfig.qbo.clientId}:${envConfig.qbo.clientSecret}`
        ).toString("base64")}`,
      },
    });

    // Check if the response is valid
    if (
      response.status !== 200 ||
      !response.data.access_token ||
      !response.data.refresh_token
    ) {
      throw new Error("Failed to refresh QBO token");
    }

    // Extract the new tokens
    const { access_token, refresh_token, expires_in } = response.data;

    // Calculate new expiration time
    const expiresAt = new Date(now.getTime() + expires_in * 1000);

    // Update the authentication in the database
    await prisma.accountingPlatformIntegration.update({
      where: {
        id: connectionId,
      },
      data: {
        authentication: {
          accessToken: access_token,
          refreshToken: refresh_token,
          expiresAt: expiresAt.toISOString(),
          lastRefreshedAt: now.toISOString(),
        },
      },
    });

    logger.info(
      `Successfully refreshed QBO token for integration ${connectionId}`
    );

    // Return the new access token and realm ID
    return {
      accessToken: access_token,
      realmId,
    };
  } catch (error) {
    logger.error(`Failed to refresh QBO token: ${error}`);

    // If token refresh fails, throw an error
    throw new ApiException({
      status: HttpStatus.UNAUTHORIZED,
      code: ErrorCode.UNAUTHORIZED,
      message: "Failed to refresh QBO authentication token",
    });
  }
}

/**
 * Function to get a valid QBO token by organization ID
 * This function:
 * 1. Finds the active accounting Platform integration for the given organization ID
 * 2. Gets a valid token for that integration
 *
 * @param orgId The organization ID (zactCompanyId)
 * @returns Object containing accessToken and realmId
 */
export const getValidQboTokenByOrgId = async (
  orgId: string
): Promise<{ accessToken: string; realmId: string }> => {
  // Validate the organization ID
  if (!orgId) {
    throw new ApiException({
      status: HttpStatus.BAD_REQUEST,
      code: ErrorCode.BAD_REQUEST,
      message: "Organization ID is required",
    });
  }

  // Find the active accounting Platform integration for this organization
  const integration = await prisma.accountingPlatformIntegration.findFirst({
    where: {
      zactCompanyId: orgId,
      connectionStatus: "ACTIVE",
    },
  });

  // If no integration found, throw an error
  if (!integration) {
    throw new ApiException({
      status: HttpStatus.NOT_FOUND,
      code: ErrorCode.NOT_FOUND,
      message:
        "No active accounting Platform integration found for this organization",
    });
  }

  // Get a valid token for this integration
  // This will throw ApiException if an error occurs, which will be caught by the global error handler
  return await getValidQboToken(integration.id);
};
