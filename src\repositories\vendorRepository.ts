import prisma from "../config/db";
import logger from "../utils/logger";
import { IUnifiedVendor } from "../interfaces";

/**
 * Repository for Vendor database operations
 */
export class VendorRepository {
  /**
   * Save vendor to database
   */
  static async save(
    unifiedVendor: IUnifiedVendor,
    connectionId: string
  ): Promise<any> {
    try {
      // Validate input
      if (!unifiedVendor) {
        throw new Error("Unified vendor data is null or undefined");
      }

    

      // Prepare data for database insertion
      const vendorData = {
        connectionId: connectionId,
        externalVendorId: unifiedVendor.id,
        vendorName: unifiedVendor.vendorName,
        contactName: unifiedVendor.contactName,
        email: unifiedVendor.email,
        phone: unifiedVendor.phone,
        website: unifiedVendor.website,
        bankAccountNumber: unifiedVendor.bankAccountNumber,
        addresses: unifiedVendor.addresses || undefined,
        isActive: unifiedVendor.isActive,
        balance: unifiedVendor.balance,
        taxNumber: unifiedVendor.taxNumber,
        domain: unifiedVendor.domain,
        createdAtPlatform: unifiedVendor.createdAtPlatform,
        updatedAtPlatform: unifiedVendor.updatedAtPlatform,
      };

      // Save to database
      const savedEntity = await prisma.vendor.create({
        data: vendorData,
      });

      logger.info(`Vendor saved to database with ID: ${savedEntity.id}`);

      // Return saved entity in unified response format with camelCase
      return {
        id: savedEntity.id,
        externalVendorId: savedEntity.externalVendorId,
        vendorName: savedEntity.vendorName,
        contactName: savedEntity.contactName,
        email: savedEntity.email,
        phone: savedEntity.phone,
        website: savedEntity.website,
        bankAccountNumber: savedEntity.bankAccountNumber,
        addresses: savedEntity.addresses,
        isActive: savedEntity.isActive,
        balance: savedEntity.balance,
        taxNumber: savedEntity.taxNumber,
        domain: savedEntity.domain,
        createdAtPlatform: savedEntity.createdAtPlatform.toISOString(),
        updatedAtPlatform: savedEntity.updatedAtPlatform?.toISOString(),
        connectionId: savedEntity.connectionId,
        createdAt: savedEntity.lastSyncedAt.toISOString(),
      };
    } catch (error) {
      logger.error(`Error saving Vendor to database:`, error);
      throw error;
    }
  }

  /**
   * Find vendors by connection ID
   */
  static async findByConnectionId(connectionId: string) {
    return await prisma.vendor.findMany({
      where: { connectionId },
    });
  }

  /**
   * Find vendor by external ID
   */
  static async findByExternalId(externalId: string, connectionId: string) {
    return await prisma.vendor.findFirst({
      where: {
        externalVendorId: externalId,
        connectionId,
      },
    });
  }

  /**
   * Update vendor
   */
  static async update(id: string, data: any) {
    return await prisma.vendor.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete old vendors
   */
  static async deleteOld(connectionId: string, days: number = 90) {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return await prisma.vendor.deleteMany({
      where: { connectionId, lastSyncedAt: { lt: cutoff } },
    });
  }
}
