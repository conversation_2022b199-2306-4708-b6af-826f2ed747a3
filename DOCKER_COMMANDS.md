# 🐳 Docker & Docker Compose Commands for Beginners

Here are all the essential commands you'll need for your Zact Unified Backend project:

## 📚 **Basic Docker Concepts**

- **Container**: A running instance of your application
- **Image**: A template used to create containers
- **Docker Compose**: Tool to manage multiple containers together
- **Service**: A container defined in docker-compose.yml

---

## 🚀 **Starting & Stopping Services**

### **Start Development Environment**

```bash
# Start all basic services (app, database, kafka)
docker-compose -f docker-compose.dev.yml up -d

# Start with development tools (Kafka UI, phpMyAdmin)
docker-compose -f docker-compose.dev.yml --profile tools up -d
```

**What it does**: Creates and starts all containers in the background (`-d` = detached mode)

### **Stop All Services**

```bash
# Stop all containers
docker-compose -f docker-compose.dev.yml down

# Stop and remove volumes (deletes database data!)
docker-compose -f docker-compose.dev.yml down -v

# Stop all services including development tools
docker-compose -f docker-compose.dev.yml --profile tools  down -v
```

**What it does**: Stops and removes containers. `-v` also deletes stored data.

### **Restart Services**

```bash
# Restart all services
docker-compose -f docker-compose.dev.yml restart

# Restart specific service
docker-compose -f docker-compose.dev.yml restart app-dev
```

**What it does**: Stops and starts containers again (useful after config changes)

---

## 👀 **Monitoring & Logs**

### **Check Service Status**

```bash
# See all running containers
docker-compose -f docker-compose.dev.yml ps

# See all Docker containers (including non-compose ones)
docker ps
```

**What it does**: Shows which containers are running, their status, and ports

### **View Logs**

```bash
# View logs from all services
docker-compose -f docker-compose.dev.yml logs

# Follow logs in real-time (like tail -f)
docker-compose -f docker-compose.dev.yml logs -f

# View logs from specific service
docker-compose -f docker-compose.dev.yml logs app-dev

# View last 20 lines of logs
docker-compose -f docker-compose.dev.yml logs --tail=20 app-dev
```

**What it does**: Shows application output, errors, and debug information

---

## 🔧 **Running Commands Inside Containers**

### **Execute Commands in Running Container**

```bash
# Open a shell inside the app container
docker-compose -f docker-compose.dev.yml exec app-dev sh

# Run a single command
docker-compose -f docker-compose.dev.yml exec app-dev npm --version

# Run database migrations
docker-compose -f docker-compose.dev.yml exec app-dev npx prisma migrate deploy
```

**What it does**: Runs commands inside the container (like SSH into a server)

### **Database Operations**

```bash
# Connect to MySQL database
docker-compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -pdevpassword zact_unified_dev

# Show databases
docker-compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -pdevpassword -e "SHOW DATABASES;"

# Backup database
docker-compose -f docker-compose.dev.yml exec mysql-dev mysqldump -u root -pdevpassword zact_unified_dev > backup.sql
```

**What it does**: Interacts with the MySQL database directly

---

## 🏗️ **Building & Updating**

### **Rebuild Containers**

```bash
# Rebuild and start (when you change Dockerfile)
docker-compose -f docker-compose.dev.yml up --build -d

# Force rebuild without cache
docker-compose -f docker-compose.dev.yml build --no-cache app-dev
```

**What it does**: Recreates the container image with your latest code changes

### **Pull Latest Images**

```bash
# Update base images (mysql, kafka, etc.)
docker-compose -f docker-compose.dev.yml pull
```

**What it does**: Downloads newer versions of external images

---

## 🧹 **Cleanup Commands**

### **Remove Containers**

```bash
# Remove stopped containers
docker-compose -f docker-compose.dev.yml rm

# Force remove running containers
docker-compose -f docker-compose.dev.yml rm -f
```

### **Clean Up Docker System**

```bash
# Remove unused images
docker image prune

# Remove everything unused (be careful!)
docker system prune

# See disk usage
docker system df
```

**What it does**: Frees up disk space by removing unused Docker resources

---

## 🔍 **Troubleshooting Commands**

### **Inspect Containers**

```bash
# Get detailed container info
docker inspect zact-unified-backend-dev

# Check container resource usage
docker stats

# See container processes
docker-compose -f docker-compose.dev.yml top
```

### **Network Issues**

```bash
# List Docker networks
docker network ls

# Inspect network
docker network inspect zact_unified_backend_default
```

### **Port Issues**

```bash
# Check what's using a port
netstat -tulpn | grep :8080

# See container port mappings
docker port zact-unified-backend-dev
```

---

## 📝 **Common Workflows**

### **Daily Development Workflow**

```bash
# 1. Start your development environment
docker-compose -f docker-compose.dev.yml --profile tools up -d

# 2. Check everything is running
docker-compose -f docker-compose.dev.yml ps

# 3. View app logs while developing
docker-compose -f docker-compose.dev.yml logs -f app-dev

# 4. When done, stop everything
docker-compose -f docker-compose.dev.yml down
```

### **When Things Go Wrong**

```bash
# 1. Check logs for errors
docker-compose -f docker-compose.dev.yml logs app-dev

# 2. Restart the problematic service
docker-compose -f docker-compose.dev.yml restart app-dev

# 3. If still broken, rebuild
docker-compose -f docker-compose.dev.yml up --build app-dev -d

# 4. Nuclear option - start fresh
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up --build -d
```

### **Database Reset**

```bash
# 1. Stop services
docker-compose -f docker-compose.dev.yml down

# 2. Remove database volume
docker volume rm zact_unified_backend_mysql_dev_data

# 3. Start again (will create fresh database)
docker-compose -f docker-compose.dev.yml up -d

# 4. Run migrations
docker-compose -f docker-compose.dev.yml exec app-dev npx prisma migrate deploy
```

---

## 🌐 **Your Service URLs**

| Service          | URL                          | Purpose             |
| ---------------- | ---------------------------- | ------------------- |
| **Main App**     | http://localhost:8080        | Your API            |
| **Health Check** | http://localhost:8080/health | App status          |
| **Kafka UI**     | http://localhost:8081        | Message management  |
| **phpMyAdmin**   | http://localhost:8082        | Database management |

---

## ⚠️ **Important Notes**

1. **Always use `-f docker-compose.dev.yml`** for development
2. **The `-d` flag** runs containers in background (detached)
3. **Use `--profile tools`** to start optional development tools
4. **Logs are your friend** - always check them when something breaks
5. **`docker-compose down -v`** will delete your database data!
6. **Hot reload is enabled** - your code changes automatically restart the app

---

## 🆘 **Emergency Commands**

```bash
# Stop everything Docker-related
docker stop $(docker ps -q)

# Remove all containers
docker rm $(docker ps -aq)

# Free up space
docker system prune -a

# Start fresh
docker-compose -f docker-compose.dev.yml up --build -d
```

**Remember**: Docker containers are disposable - you can always delete and recreate them! Your code is safe in your project folder. 🛡️
