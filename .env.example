# ==============================================
# ZACT UNIFIED BACKEND ENVIRONMENT VARIABLES
# ==============================================

# ==============================================
# SERVER CONFIGURATION
# ==============================================
PORT=3000
NODE_ENV=development

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
DATABASE_URL="postgresql://postgres:password@localhost:5432/zact_unified_db?schema=public"

# ==============================================
# JWT CONFIGURATION
# ==============================================
JWT_SECRET=your_jwt_secret_key_here_make_it_long_and_secure
JWT_EXPIRES_IN=1d

# ==============================================
# KAFKA CONFIGURATION
# ==============================================
KAFKA_CLIENT_ID=zact-unified-backend
KAFKA_BROKERS=localhost:9092
KAFKA_SSL=false
KAFKA_SASL=false
KAFKA_SASL_MECHANISM=plain
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=
KAFKA_CONNECTION_TIMEOUT=3000
KAFKA_REQUEST_TIMEOUT=30000
KAFKA_CONSUMER_GROUP_ID=zact-unified-backend-group

# ==============================================
# LOGGING CONFIGURATION
# ==============================================
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=10m
LOG_MAX_FILES=7

# ==============================================
# QBO (QUICKBOOKS ONLINE) CONFIGURATION
# ==============================================
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret
QBO_REDIRECT_URI=http://localhost:3000/auth/qbo/callback
QBO_ENVIRONMENT=sandbox
QBO_API_BASE_URL=https://quickbooks.api.intuit.com
QBO_API_BASE_URL_SANDBOX=https://sandbox-quickbooks.api.intuit.com
QBO_TOKEN_URL=https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
QBO_AUTH_URL=https://appcenter.intuit.com/connect/oauth2
QBO_REVOKE_URL=https://developer.api.intuit.com/v2/oauth2/tokens/revoke
QBO_ACCESS_TOKEN=
QBO_COMPANY_ID=

# ==============================================
# API GATEWAY CONFIGURATION
# ==============================================
API_GATEWAY_URL=http://localhost:8000
API_TIMEOUT=30000

# ==============================================
# SYNC CONFIGURATION
# ==============================================
MAX_SYNC_RETRIES=3
SYNC_RETRY_DELAY=1000
SYNC_BATCH_SIZE=50
SYNC_MAX_BATCH_RETRIES=3
SYNC_DEFAULT_CLEANUP_DAYS=90
MAX_CONCURRENT_SYNCS=3

# ==============================================
# KAFKA MESSAGE CONFIGURATION
# ==============================================
KAFKA_MESSAGE_BATCH_SIZE=500
KAFKA_MESSAGE_BATCH_DELAY_MS=100

# ==============================================
# CRON JOB CONFIGURATION
# ==============================================
# Incremental Sync Cron Job
INCREMENTAL_SYNC_CRON_ENABLED=false
INCREMENTAL_SYNC_CRON_SCHEDULE=0 */6 * * *
INCREMENTAL_SYNC_CRON_BATCH_SIZE=1000
INCREMENTAL_SYNC_CRON_ENTITIES=ACCOUNT,VENDOR,CLASS
INCREMENTAL_SYNC_CRON_MAX_CONCURRENT=3
INCREMENTAL_SYNC_CRON_RETRY_ATTEMPTS=3
INCREMENTAL_SYNC_CRON_RETRY_DELAY=5000
CRON_TIMEZONE=UTC

