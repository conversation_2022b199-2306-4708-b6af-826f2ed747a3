import { PrismaClient } from "@prisma/client";
import logger from "../utils/logger";

const prisma = new PrismaClient();

prisma
  .$connect()
  .then(() => {
    logger.info("Database connected.");
  })
  .catch((error) => {
    console.error("Error connecting to the database:", error);
  })
  .finally(() => {
    // Optionally, you can close the connection when the application exits
    process.on("exit", () => {
      prisma.$disconnect();
    });
  });

export default prisma;
