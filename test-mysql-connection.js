const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testConnection() {
  try {
    console.log('Testing MySQL connection...');
    
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Successfully connected to MySQL database');
    
    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Query test successful:', result);
    
    // Test table existence by counting tables
    const tables = await prisma.$queryRaw`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'zact_staging'
    `;
    console.log('✅ Database tables created:', tables[0].table_count, 'tables found');
    
    // List all tables
    const tableList = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'zact_staging'
      ORDER BY table_name
    `;
    console.log('📋 Tables in database:');
    tableList.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Disconnected from database');
  }
}

testConnection();
