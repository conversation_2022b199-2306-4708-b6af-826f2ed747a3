import { IQboBillResponse, IUnifiedBill } from "../interfaces";

export const mapQboBillResponseToUnified = (
  response: IQboBillResponse
): IUnifiedBill => {
  return {
    id: response.Id,
    vendorId: response.VendorRef?.value ?? "",
    externalBillId: response.Id,
    billNumber: response.Id, // QBO doesn't have separate bill number, using ID
    billDate: response.TxnDate ?? new Date().toISOString(),
    dueDate: response.DueDate ?? new Date().toISOString(),
    totalAmount: response.TotalAmt,
    balance: response.Balance,
    currency: response.CurrencyRef?.value ?? "USD",
    status: "OPEN", // Default status, QBO doesn't provide this directly
    privateNote: null,
    exchangeRate: null,
    lineItems: response.Line.map((line) => ({
      description: line.Description ?? null,
      amount: line.Amount,
      accountId: line.AccountBasedExpenseLineDetail?.AccountRef?.value ?? null,
      taxRateId: line.AccountBasedExpenseLineDetail?.TaxCodeRef?.value ?? null,
      customerId:
        line.AccountBasedExpenseLineDetail?.CustomerRef?.value ?? null,
      projectId: line.ProjectRef?.value ?? null,
      detailType: line.DetailType ?? "AccountBasedExpenseLineDetail",
    })),
    accountId: response.APAccountRef?.value ?? null,
    classId: null,
    createdAtPlatform: response.MetaData?.CreateTime
      ? new Date(response.MetaData.CreateTime)
      : new Date(),
    updatedAtPlatform: response.MetaData?.LastUpdatedTime
      ? new Date(response.MetaData.LastUpdatedTime)
      : null,
    domain: response.domain,
  };
};

export const mapUnifiedToQboBillRequest = (
  bill: Partial<IUnifiedBill>
): Partial<IQboBillResponse> => {
  return {
    VendorRef: bill.vendorId
      ? {
          value: bill.vendorId,
        }
      : undefined,
    TxnDate: bill.billDate ?? new Date().toISOString().split("T")[0],
    DueDate: bill.dueDate ?? undefined,
    TotalAmt: bill.totalAmount ?? 0,
    CurrencyRef: bill.currency ? { value: bill.currency } : undefined,
    APAccountRef: bill.accountId ? { value: bill.accountId } : undefined,
    Line:
      bill.lineItems?.map((line) => ({
        Description: line.description ?? undefined,
        Amount: line.amount,
        DetailType: line.detailType ?? "AccountBasedExpenseLineDetail",
        AccountBasedExpenseLineDetail: {
          AccountRef: line.accountId
            ? {
                value: line.accountId,
              }
            : undefined,
          TaxCodeRef: line.taxRateId
            ? {
                value: line.taxRateId,
              }
            : undefined,
          CustomerRef: line.customerId
            ? {
                value: line.customerId,
              }
            : undefined,
        },
        ProjectRef: line.projectId
          ? {
              value: line.projectId,
            }
          : undefined,
      })) ?? [],
    domain: bill.domain ?? "",
  };
};

/**
 * Map QBO Bill response to unified format and save to database
 * @deprecated Use BillRepository.save() instead
 */
export const mapAndSaveBill = async (
  qboBill: any,
  connectionId: string
): Promise<any> => {
  // Import here to avoid circular dependencies
  const { BillRepository } = await import("../repositories");

  // Use existing mapper to convert QBO response to unified format
  const unifiedBill = mapQboBillResponseToUnified(qboBill);

  // Use repository to save
  return await BillRepository.save(unifiedBill, connectionId);
};
