import { SyncedEntityType, SyncOperationType } from "@prisma/client";
import baseProducer from "../../kafka/producers/base-producer";
import { v4 as uuidv4 } from "uuid";
import prisma from "../../config/db";
import { EntityType, ERPSystem } from "../../interfaces/kafkaMessageInterface";
import {
  createEntityDataBatchMessage,
  createErrorMessage,
  createSyncInfoMessage,
  createInstantSyncMessage,
  createScheduledSyncMessage,
} from "../../utils/kafka-messageUtils";
import logger from "../../utils/logger";
import { initiateFullSync } from "../sync/initiateSync";
import { getMessageSizeInKB } from "./../../utils/helpers";

// Import configuration and constants
import { SYNC_CONFIG } from "../../constants/syncConfig";
import { KAFKA_TOPICS } from "../../constants/kafkaTopics";
import { envConfig } from "../../config/config";

// Configuration
const CONFIG = {
  BATCH_SIZE: envConfig.kafkaMessage.batchSize,
  SYNC_COMPLETION_TOPIC: KAFKA_TOPICS.SYNC_COMPLETION_RESPONSE,
  BATCH_STREAM: KAFKA_TOPICS.ENTITY_BATCH_STREAM,
  BATCH_DELAY_MS: envConfig.kafkaMessage.batchDelayMs,
};

// Entity mapping
const ENTITY_TYPE_MAPPING: Record<SyncedEntityType, EntityType> = {
  [SyncedEntityType.ACCOUNT]: EntityType.ACCOUNT,
  [SyncedEntityType.VENDOR]: EntityType.VENDOR,
  [SyncedEntityType.CLASS]: EntityType.CLASS,
  [SyncedEntityType.BILL]: EntityType.BILL,
  [SyncedEntityType.PAYMENT]: EntityType.PAYMENT,
  [SyncedEntityType.JOURNAL_ENTRY]: EntityType.JOURNAL_ENTRY,
};

// Supported entities for sync
const SUPPORTED_ENTITIES = [
  SyncedEntityType.ACCOUNT,
  SyncedEntityType.VENDOR,
  SyncedEntityType.CLASS,
];

/**
 * Send message via Kafka
 */
const sendKafkaMessage = async (message: any, topic: string): Promise<void> => {
  try {
    // Validate message structure||
    if (!message || !message.securityContext.connectionId) {
      throw new Error("Invalid message structure: connectionId is required");
    }

    await baseProducer.sendMessage(
      topic,
      message,
      message.securityContext.connectionId
    );

    logger.info("Message sent to Kafka", {
      messageId: message.messageId,
      correlationId: message.correlationId,
      messageType: message.messageType,
      statusCode: message.status?.code,
      topic: topic,
    });
  } catch (error: any) {
    logger.error("Failed to send Kafka message", {
      error: error.message,
      messageId: message.messageId,
      correlationId: message.correlationId,
    });
    throw error;
  }
};

/**
 * Fetch entity data from database with pagination
 */
const fetchEntityBatches = async (
  connectionId: string,
  entityType: SyncedEntityType
): Promise<{ batches: any[][]; totalCount: number }> => {
  const batches: any[][] = [];
  let offset = 0;
  let totalCount = 0;

  // Get total count first
  switch (entityType) {
    case SyncedEntityType.ACCOUNT:
      totalCount = await prisma.account.count({
        where: { connectionId },
      });
      break;
    case SyncedEntityType.VENDOR:
      totalCount = await prisma.vendor.count({
        where: { connectionId },
      });
      break;
    case SyncedEntityType.CLASS:
      totalCount = await prisma.class.count({
        where: { connectionId },
      });
      break;
    default:
      logger.warn(`Unsupported entity type for batching: ${entityType}`);
      return { batches: [], totalCount: 0 };
  }

  if (totalCount === 0) {
    logger.info(
      `No records found for ${entityType} in connection ${connectionId}`
    );
    return { batches: [], totalCount: 0 };
  }

  // Fetch data in batches
  while (offset < totalCount) {
    let batchData: any[] = [];

    try {
      switch (entityType) {
        case SyncedEntityType.ACCOUNT:
          batchData = await prisma.account.findMany({
            where: { connectionId },
            skip: offset,
            take: CONFIG.BATCH_SIZE,
            orderBy: { id: "asc" },
          });
          break;

        case SyncedEntityType.VENDOR:
          batchData = await prisma.vendor.findMany({
            where: { connectionId },
            skip: offset,
            take: CONFIG.BATCH_SIZE,
            orderBy: { id: "asc" },
          });
          break;

        case SyncedEntityType.CLASS:
          batchData = await prisma.class.findMany({
            where: { connectionId },
            skip: offset,
            take: CONFIG.BATCH_SIZE,
            orderBy: { id: "asc" },
          });
          break;
      }

      if (batchData.length > 0) {
        batches.push(batchData);
      }

      offset += CONFIG.BATCH_SIZE;
    } catch (error: any) {
      logger.error(`Error fetching batch for ${entityType}`, {
        error: error.message,
        offset,
        batchSize: CONFIG.BATCH_SIZE,
        connectionId,
      });
      throw error;
    }
  }

  logger.info(`Fetched ${batches.length} batches for ${entityType}`, {
    totalRecords: totalCount,
    totalBatches: batches.length,
    connectionId,
  });

  return { batches, totalCount };
};

/**
 * Send entity data batches via Kafka
 */
const sendEntityDataBatches = async (
  correlationId: string,
  connectionId: string,
  entityType: SyncedEntityType,
  erpSystem: ERPSystem = ERPSystem.QBO
): Promise<{ batchesSent: number; recordsSent: number; success: boolean }> => {
  try {
    const messageEntityType = ENTITY_TYPE_MAPPING[entityType];
    const { batches, totalCount } = await fetchEntityBatches(
      connectionId,
      entityType
    );

    if (batches.length === 0) {
      logger.info(`No data to send for ${entityType}`, {
        connectionId,
        correlationId,
      });
      return { batchesSent: 0, recordsSent: 0, success: true };
    }

    logger.info(`Sending ${batches.length} batches for ${entityType}`, {
      correlationId,
      connectionId,
      totalBatches: batches.length,
      totalRecords: totalCount,
    });

    let recordsSent = 0;

    // Send each batch
    for (let i = 0; i < batches.length; i++) {
      const batchNumber = i + 1;
      const batch = batches[i];

      const batchMessage = createEntityDataBatchMessage(
        correlationId,
        connectionId,
        messageEntityType,
        batch,
        batchNumber,
        batches.length,
        batch.length,
        ERPSystem.QBO
      );

      // Calculate message size in KB

      await sendKafkaMessage(batchMessage, CONFIG.BATCH_STREAM);
      recordsSent += batch.length;

      logger.debug(
        `Sent batch ${batchNumber}/${batches.length} for ${entityType}`,
        {
          batchSize: batch.length,
          totalSent: recordsSent,
          correlationId,
        }
      );

      // Add delay between batches to avoid overwhelming consumer
      if (i < batches.length - 1 && CONFIG.BATCH_DELAY_MS > 0) {
        await new Promise((resolve) =>
          setTimeout(resolve, CONFIG.BATCH_DELAY_MS)
        );
      }
    }

    logger.info(`Successfully sent all batches for ${entityType}`, {
      batchesSent: batches.length,
      recordsSent,
      correlationId,
    });

    return { batchesSent: batches.length, recordsSent, success: true };
  } catch (error: any) {
    logger.error(`Failed to send data batches for ${entityType}`, {
      error: error.message,
      correlationId,
      connectionId,
    });

    // Send error message
    const errorMessage = createErrorMessage(
      correlationId,
      connectionId,
      "BATCH_SEND_ERROR",
      `Failed to send data batches for ${entityType}: ${error.message}`,
      ENTITY_TYPE_MAPPING[entityType],
      erpSystem
    );

    try {
      await sendKafkaMessage(errorMessage, CONFIG.BATCH_STREAM);
    } catch (kafkaError: any) {
      logger.error("Failed to send error message to Kafka", {
        originalError: error.message,
        kafkaError: kafkaError.message,
        correlationId,
      });
    }

    return { batchesSent: 0, recordsSent: 0, success: false };
  }
};

/**
 * Get processed counts from sync operations
 */
const getProcessedCounts = async (
  connectionId: string
): Promise<Record<string, number>> => {
  try {
    const syncResults = await prisma.syncOperation.findMany({
      where: {
        connectionId,
        status: "COMPLETED",
        syncCompletedAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
        },
      },
      orderBy: { syncCompletedAt: "desc" },
      take: 10, // Get recent operations
    });

    const processedCounts: Record<string, number> = {
      account: 0,
      vendor: 0,
      class: 0,
    };

    // Map entity types and sum successful records
    syncResults.forEach((sync: any) => {
      const entityName = sync.entityType.toLowerCase();
      if (processedCounts.hasOwnProperty(entityName)) {
        processedCounts[entityName] = sync.recordsSucceeded || 0;
      }
    });

    return processedCounts;
  } catch (error: any) {
    logger.error("Failed to get processed counts", {
      error: error.message,
      connectionId,
    });

    // Return zero counts on error
    return { account: 0, vendor: 0, class: 0 };
  }
};

/**
 * Main function to handle post-sync messaging
 */
export const handlePostSyncMessaging = async (
  connectionId: string,
  erpSystem: ERPSystem = ERPSystem.QBO,
  syncOperationType?: SyncOperationType
): Promise<void> => {
  const correlationId = uuidv4();
  const startTime = Date.now();

  // For instant sync, send completion message but skip data batches since they were already sent
  const isInstantSync =
    syncOperationType === SyncOperationType.INCREMENTAL_SYNC;

  if (isInstantSync) {
    logger.info(
      "Post-sync messaging for instant sync - sending completion only",
      {
        correlationId,
        connectionId,
        syncOperationType,
      }
    );
  } else {
    logger.info("Starting post-sync messaging", {
      correlationId,
      connectionId,
      supportedEntities: SUPPORTED_ENTITIES,
      syncOperationType,
    });
  }

  try {
    // 1. Get processed counts and send INFO message
    const processedCounts = await getProcessedCounts(connectionId);
    const totalProcessed = Object.values(processedCounts).reduce(
      (sum, count) => sum + count,
      0
    );

    if (totalProcessed === 0) {
      logger.warn("No successfully processed records found for messaging", {
        correlationId,
        connectionId,
      });
      return;
    }

    const infoMessage = createSyncInfoMessage(
      correlationId,
      connectionId,
      processedCounts,
      erpSystem,
      Date.now() - startTime
    );

    await sendKafkaMessage(infoMessage, CONFIG.SYNC_COMPLETION_TOPIC);

    // 2. Send data batches for each entity (skip for instant sync since data already sent)
    if (isInstantSync) {
      logger.info(
        "Skipping data batch sending for instant sync - data already sent via entity-batch-stream",
        {
          correlationId,
          connectionId,
          processedCounts,
        }
      );
      return;
    }

    const batchResults: Record<string, any> = {};

    for (const entityType of SUPPORTED_ENTITIES) {
      const entityName = entityType.toLowerCase();

      if (processedCounts[entityName] > 0) {
        logger.info(`Starting batch sending for ${entityName}`, {
          correlationId,
          processedCount: processedCounts[entityName],
        });

        const result = await sendEntityDataBatches(
          correlationId,
          connectionId,
          entityType,
          erpSystem
        );

        batchResults[entityName] = result;

        if (!result.success) {
          logger.error(`Failed to send batches for ${entityName}`, {
            correlationId,
            result,
          });
        }
      } else {
        logger.info(`Skipping ${entityName} - no records processed`, {
          correlationId,
        });
      }
    }

    const totalDuration = Date.now() - startTime;
    const summary = {
      correlationId,
      connectionId,
      totalDuration,
      processedCounts,
      batchResults,
      messagesType: "INFO + DATA_BATCHES",
    };

    logger.info("Post-sync messaging completed successfully", summary);
  } catch (error: any) {
    const totalDuration = Date.now() - startTime;

    logger.error("Critical error during post-sync messaging", {
      error: error.message,
      correlationId,
      connectionId,
      duration: totalDuration,
    });

    // Send critical error message
    try {
      const errorMessage = createErrorMessage(
        correlationId,
        connectionId,
        "POST_SYNC_CRITICAL_ERROR",
        `Critical error during post-sync messaging: ${error.message}`,
        undefined,
        erpSystem,
        totalDuration
      );

      await sendKafkaMessage(errorMessage, CONFIG.SYNC_COMPLETION_TOPIC);
    } catch (kafkaError: any) {
      logger.error("Failed to send critical error message", {
        originalError: error.message,
        kafkaError: kafkaError.message,
        correlationId,
      });
    }

    throw error;
  }
};

/**
 * Integration function for your existing sync flow
 */
export const executeFullSyncWithMessaging = async (
  service: string,
  connectionId: string,
  accessToken: string,
  realmId: string,
  syncOperationType: SyncOperationType = SyncOperationType.FULL_SYNC
): Promise<void> => {
  logger.info(
    `Starting ${syncOperationType} sync with messaging for connection ${connectionId}`
  );

  try {
    // 1. Execute the sync with specified operation type
    await initiateFullSync(
      service,
      connectionId,
      accessToken,
      realmId,
      syncOperationType
    );

    logger.info(
      `${syncOperationType} sync completed for connection ${connectionId}`
    );

    // 2. Handle post-sync messaging
    await handlePostSyncMessaging(
      connectionId,
      ERPSystem.QBO,
      syncOperationType
    );

    logger.info(
      `${syncOperationType} sync with messaging completed for connection ${connectionId}`
    );
  } catch (syncError: any) {
    logger.error(
      `Error during ${syncOperationType} sync with messaging for connection ${connectionId}`,
      {
        error: syncError.message,
        connectionId,
        syncType: syncOperationType,
      }
    );
    throw syncError;
  }
};

/**
 * Send instant sync data with create/update differentiation
 */
export const sendInstantSyncData = async (
  correlationId: string,
  connectionId: string,
  entityType: SyncedEntityType,
  createdRecords: any[],
  updatedRecords: any[],
  erpSystem: ERPSystem = ERPSystem.QBO
): Promise<{ batchesSent: number; recordsSent: number; success: boolean }> => {
  try {
    const messageEntityType = ENTITY_TYPE_MAPPING[entityType];
    const totalRecords = createdRecords.length + updatedRecords.length;

    if (totalRecords === 0) {
      logger.info(`No data to send for instant sync ${entityType}`, {
        connectionId,
        correlationId,
      });
      return { batchesSent: 0, recordsSent: 0, success: true };
    }

    // Calculate batches based on total records
    const allRecords = [...createdRecords, ...updatedRecords];
    const totalBatches = Math.ceil(totalRecords / CONFIG.BATCH_SIZE);
    let recordsSent = 0;
    let batchesSent = 0;

    logger.info(`Sending instant sync data for ${entityType}`, {
      correlationId,
      connectionId,
      totalBatches,
      totalRecords,
      created: createdRecords.length,
      updated: updatedRecords.length,
    });

    // Send data in batches
    for (let i = 0; i < totalBatches; i++) {
      const batchNumber = i + 1;
      const startIndex = i * CONFIG.BATCH_SIZE;
      const endIndex = Math.min(startIndex + CONFIG.BATCH_SIZE, totalRecords);

      // Determine which records belong to this batch
      const batchCreated: any[] = [];
      const batchUpdated: any[] = [];

      // First, add created records to batches
      let currentIndex = 0;
      for (const record of createdRecords) {
        if (currentIndex >= startIndex && currentIndex < endIndex) {
          batchCreated.push(record);
        }
        currentIndex++;
      }

      // Then, add updated records to batches
      for (const record of updatedRecords) {
        if (currentIndex >= startIndex && currentIndex < endIndex) {
          batchUpdated.push(record);
        }
        currentIndex++;
      }

      const batchMessage = createInstantSyncMessage(
        correlationId,
        connectionId,
        messageEntityType,
        batchCreated,
        batchUpdated,
        batchNumber,
        totalBatches,
        erpSystem
      );

      await sendKafkaMessage(batchMessage, CONFIG.BATCH_STREAM);
      recordsSent += batchCreated.length + batchUpdated.length;
      batchesSent++;

      logger.debug(
        `Sent instant sync batch ${batchNumber}/${totalBatches} for ${entityType}`,
        {
          batchCreated: batchCreated.length,
          batchUpdated: batchUpdated.length,
          totalSent: recordsSent,
          correlationId,
        }
      );

      // Add delay between batches
      if (i < totalBatches - 1 && CONFIG.BATCH_DELAY_MS > 0) {
        await new Promise((resolve) =>
          setTimeout(resolve, CONFIG.BATCH_DELAY_MS)
        );
      }
    }

    logger.info(`Successfully sent incremental sync data for ${entityType}`, {
      batchesSent,
      recordsSent,
      correlationId,
    });

    return { batchesSent, recordsSent, success: true };
  } catch (error: any) {
    logger.error(`Failed to send incremental sync data for ${entityType}`, {
      error: error.message,
      correlationId,
      connectionId,
    });

    // Send error message
    const errorMessage = createErrorMessage(
      correlationId,
      connectionId,
      "INCREMENTAL_SYNC_SEND_ERROR",
      `Failed to send incremental sync data for ${entityType}: ${error.message}`,
      ENTITY_TYPE_MAPPING[entityType],
      erpSystem
    );

    try {
      await sendKafkaMessage(errorMessage, CONFIG.BATCH_STREAM);
    } catch (kafkaError: any) {
      logger.error("Failed to send instant sync error message to Kafka", {
        originalError: error.message,
        kafkaError: kafkaError.message,
        correlationId,
      });
    }

    return { batchesSent: 0, recordsSent: 0, success: false };
  }
};

/**
 * Send scheduled sync data with create/update differentiation
 */
export const sendScheduledSyncData = async (
  correlationId: string,
  connectionId: string,
  entityType: SyncedEntityType,
  createdRecords: any[],
  updatedRecords: any[],
  erpSystem: ERPSystem = ERPSystem.QBO
): Promise<{ batchesSent: number; recordsSent: number; success: boolean }> => {
  try {
    const messageEntityType = ENTITY_TYPE_MAPPING[entityType];
    const totalRecords = createdRecords.length + updatedRecords.length;

    if (totalRecords === 0) {
      logger.info(`No data to send for scheduled sync ${entityType}`, {
        connectionId,
        correlationId,
      });
      return { batchesSent: 0, recordsSent: 0, success: true };
    }

    // Calculate batches based on total records
    const totalBatches = Math.ceil(totalRecords / CONFIG.BATCH_SIZE);
    let recordsSent = 0;
    let batchesSent = 0;

    logger.info(`Sending scheduled sync data for ${entityType}`, {
      connectionId,
      correlationId,
      totalRecords,
      totalBatches,
      created: createdRecords.length,
      updated: updatedRecords.length,
    });

    // Process batches
    for (let i = 0; i < totalBatches; i++) {
      const batchNumber = i + 1;
      const startIndex = i * CONFIG.BATCH_SIZE;
      const endIndex = Math.min(startIndex + CONFIG.BATCH_SIZE, totalRecords);

      // Calculate how many records from each type for this batch
      let batchCreated: any[] = [];
      let batchUpdated: any[] = [];

      if (startIndex < createdRecords.length) {
        const createdEndIndex = Math.min(endIndex, createdRecords.length);
        batchCreated = createdRecords.slice(startIndex, createdEndIndex);
      }

      const remainingBatchSize = CONFIG.BATCH_SIZE - batchCreated.length;
      if (remainingBatchSize > 0 && updatedRecords.length > 0) {
        const updatedStartIndex = Math.max(
          0,
          startIndex - createdRecords.length
        );
        const updatedEndIndex = Math.min(
          updatedStartIndex + remainingBatchSize,
          updatedRecords.length
        );
        if (updatedStartIndex < updatedRecords.length) {
          batchUpdated = updatedRecords.slice(
            updatedStartIndex,
            updatedEndIndex
          );
        }
      }

      const batchMessage = createScheduledSyncMessage(
        correlationId,
        connectionId,
        messageEntityType,
        batchCreated,
        batchUpdated,
        batchNumber,
        totalBatches,
        erpSystem
      );

      await sendKafkaMessage(batchMessage, CONFIG.BATCH_STREAM);
      recordsSent += batchCreated.length + batchUpdated.length;
      batchesSent++;

      logger.debug(
        `Sent scheduled sync batch ${batchNumber}/${totalBatches} for ${entityType}`,
        {
          batchCreated: batchCreated.length,
          batchUpdated: batchUpdated.length,
          totalSent: recordsSent,
          correlationId,
        }
      );

      // Add delay between batches
      if (i < totalBatches - 1 && CONFIG.BATCH_DELAY_MS > 0) {
        await new Promise((resolve) =>
          setTimeout(resolve, CONFIG.BATCH_DELAY_MS)
        );
      }
    }

    logger.info(`Successfully sent scheduled sync data for ${entityType}`, {
      batchesSent,
      recordsSent,
      correlationId,
    });

    return { batchesSent, recordsSent, success: true };
  } catch (error: any) {
    logger.error(`Failed to send scheduled sync data for ${entityType}`, {
      error: error.message,
      correlationId,
      connectionId,
    });

    // Send error message
    const errorMessage = createErrorMessage(
      correlationId,
      connectionId,
      "SCHEDULED_SYNC_SEND_ERROR",
      `Failed to send scheduled sync data for ${entityType}: ${error.message}`,
      ENTITY_TYPE_MAPPING[entityType],
      erpSystem
    );

    try {
      await sendKafkaMessage(errorMessage, CONFIG.BATCH_STREAM);
    } catch (kafkaError: any) {
      logger.error("Failed to send scheduled sync error message to Kafka", {
        originalError: error.message,
        kafkaError: kafkaError.message,
        correlationId,
      });
    }

    return { batchesSent: 0, recordsSent: 0, success: false };
  }
};

export default {
  handlePostSyncMessaging,
  executeFullSyncWithMessaging,
  sendEntityDataBatches,
  sendInstantSyncData,
  getProcessedCounts,
};
