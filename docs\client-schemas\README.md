# Client Entity Schemas

This directory contains JSON schemas and examples for all entity types that can be sent to Kafka in the unified backend system.

## Overview

The unified backend system processes entities from various accounting platforms (QBO, Xero, NetSuite) through a standardized format. All entities are sent to Kafka topics as JSON messages with a consistent structure.

## Kafka Topics

- **entity-create-request**: Topic for sending entity creation requests
- **entity-create-response**: Topic for receiving entity creation responses

## Message Structure

All Kafka messages follow the structure defined in `kafka-message-schema.json`. The key components are:

- **Message Metadata**: messageId, correlationId, timestamp, source, destination
- **Operation Context**: messageType, erpSystem, entityOperation
- **Security**: securityContext with connectionId
- **Data**: payload array containing unified entity objects

## Entity Types

### 1. Account (`account-schema.json`)

Financial accounts from the chart of accounts.

**Required Fields:**

- `id`, `fullyQualifiedName`, `name`, `accountType`, `active`, `currentBalance`, `subAccount`, `domain`

**Example Use Cases:**

- Creating new accounts in the accounting system
- Syncing chart of accounts

### 2. Vendor (`vendor-schema.json`)

Vendor/supplier information.

**Required Fields:**

- `id`, `vendorName`, `isActive`, `domain`

**Example Use Cases:**

- Adding new vendors
- Updating vendor information

### 3. Class (`class-schema.json`)

Class/department tracking for expenses and income.

**Required Fields:**

- `id`, `name`, `status`, `hasChildren`, `createdAtPlatform`, `updatedAtPlatform`, `domain`

**Example Use Cases:**

- Creating department classifications
- Setting up project tracking

### 4. Bill (`bill-schema.json`)

Bills/invoices from vendors.

**Required Fields:**

- `id`, `vendorId`, `billNumber`, `billDate`, `dueDate`, `totalAmount`, `balance`, `currency`, `status`, `lineItems`

**Example Use Cases:**

- Recording vendor bills
- Expense management

### 5. Payment (`payment-schema.json`)

Payments made to vendors.

**Required Fields:**

- `id`, `vendorId`, `paymentType`, `totalAmount`, `paymentDate`, `currency`, `billPayments`

**Payment Types:**

- `Check`: Requires `bankAccountId`
- `CreditCard`: Requires `creditCardAccountId`
- `Cash`: No additional requirements

### 6. Journal Entry (`journal-entry-schema.json`)

Manual journal entries for accounting adjustments.

**Required Fields:**

- `id`, `domain`, `transactionDate`, `lineItems`

**Line Items Requirements:**

- Must have at least 2 line items
- Each line item needs `postingType` (Debit/Credit), `totalAmount`, `accountId`
- Debits and credits must balance

## Platform-Specific Requirements

The schemas include comments indicating which fields are required by specific platforms:

- **MANDATORY**: Required by ALL platforms
- **Optional**: Platform-specific requirements noted in field descriptions
- **QBO**: QuickBooks Online
- **Xero**: Xero accounting
- **NetSuite**: Oracle NetSuite

## Validation

Each entity type has corresponding validation functions:

- `validateVendorRequest()`
- `validateBillRequest()`
- `validatePaymentRequest()`
- `validateJournalEntryRequest()`

## Example Usage

### Creating a Vendor

```json
{
  "messageId": "550e8400-e29b-41d4-a716-************",
  "correlationId": "550e8400-e29b-41d4-a716-************",
  "timestamp": *************,
  "source": "ZACT_APP",
  "destination": "UNIFIED_BACKEND",
  "messageType": "REQUEST",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "vendor",
    "operation": "CREATE"
  },
  "securityContext": {
    "connectionId": "conn-123-456-789"
  },
  "payload": [
    {
      "id": "123",
      "vendorName": "ABC Office Supplies",
      "isActive": true,
      "domain": "qbo"
    }
  ]
}
```

### Creating a Bill

```json
{
  "messageId": "550e8400-e29b-41d4-a716-************",
  "correlationId": "550e8400-e29b-41d4-a716-************",
  "timestamp": *************,
  "source": "ZACT_APP",
  "destination": "UNIFIED_BACKEND",
  "messageType": "REQUEST",
  "erpSystem": "QBO",
  "entityOperation": {
    "entityType": "bill",
    "operation": "CREATE"
  },
  "securityContext": {
    "connectionId": "conn-123-456-789"
  },
  "payload": [
    {
      "id": "BILL001",
      "vendorId": "123",
      "billNumber": "INV-2024-001",
      "billDate": "2024-01-15",
      "dueDate": "2024-02-14",
      "totalAmount": 1250.0,
      "balance": 1250.0,
      "currency": "USD",
      "status": "OPEN",
      "lineItems": [
        {
          "description": "Office supplies",
          "amount": 1250.0,
          "accountId": "456"
        }
      ]
    }
  ]
}
```

## Response Format

Successful operations will receive responses on the `entity-create-response` topic with the created entity data mapped back to unified format.

Error responses will include validation errors or API errors in the response message.

## Important ID Field Rules

### Inbound Requests (entity-create-request)

- **DO NOT include `id` field** - This will be generated by the database
- **DO NOT include `externalId` field** - This will be provided by the accounting platform after creation

### Outbound Responses (entity-create-response, sync messages)

- **`id`**: Database-generated UUID (e.g., "db-uuid-vendor-123")
- **`externalId`**: Platform-provided ID (e.g., "123" from QBO)
- Both fields will always be present in outbound messages

## Best Practices

1. **Always include required fields** marked as MANDATORY
2. **Use proper date formats** (ISO 8601 for dates, Unix timestamps for message timestamps)
3. **Validate data** before sending to ensure compliance with schemas
4. **Include correlation IDs** for tracking related messages
5. **Use appropriate entity types** in entityOperation
6. **Ensure line items balance** for journal entries (debits = credits)
7. **Never send `id` or `externalId` in create requests** - These are generated by the system

## Support

For questions about schema usage or integration, refer to the validation functions in the `src/validators/` directory or contact the development team.
