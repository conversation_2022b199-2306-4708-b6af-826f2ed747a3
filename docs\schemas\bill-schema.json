{"$schema": "http://json-schema.org/draft-07/schema#", "title": "<PERSON>", "description": "Schema for Bill entities sent to Kafka in unified format", "type": "object", "required": ["id", "vendorId", "bill<PERSON><PERSON><PERSON>", "billDate", "dueDate", "totalAmount", "balance", "currency", "status", "lineItems"], "properties": {"id": {"type": "string", "description": "Unique identifier for the bill"}, "vendorId": {"type": "string", "description": "MANDATORY - Required by ALL platforms. ID of the vendor"}, "externalBillId": {"type": ["string", "null"], "description": "External platform-specific bill ID"}, "billNumber": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Bill number or reference"}, "billDate": {"type": "string", "format": "date", "description": "MANDATORY - Required by ALL platforms. Date of the bill (ISO date string)"}, "dueDate": {"type": "string", "format": "date", "description": "MANDATORY - Required by ALL platforms. Due date for payment (ISO date string)"}, "totalAmount": {"type": "number", "minimum": 0, "description": "MANDATORY - Required by ALL platforms. Total amount of the bill"}, "balance": {"type": "number", "description": "MANDATORY - Required by ALL platforms. Outstanding balance on the bill"}, "currency": {"type": "string", "description": "MANDATORY - Required by ALL platforms. Currency code (e.g., USD, EUR)"}, "status": {"type": "string", "enum": ["DRAFT", "OPEN", "PAID", "VOID"], "description": "MANDATORY - Required by ALL platforms. Status of the bill"}, "privateNote": {"type": ["string", "null"], "description": "Private note or memo for the bill"}, "exchangeRate": {"type": ["number", "null"], "description": "Exchange rate if using foreign currency"}, "lineItems": {"type": "array", "minItems": 1, "description": "MANDATORY - Array of line items for the bill", "items": {"type": "object", "required": ["amount"], "properties": {"description": {"type": ["string", "null"], "description": "Description of the line item"}, "amount": {"type": "number", "minimum": 0, "description": "MANDATORY - Amount for this line item"}, "accountId": {"type": ["string", "null"], "description": "Account ID for the line item"}, "classId": {"type": ["string", "null"], "description": "Class ID for the line item"}, "taxCodeId": {"type": ["string", "null"], "description": "Tax code ID for the line item"}, "billableStatus": {"type": ["string", "null"], "description": "Billable status of the line item"}, "customerId": {"type": ["string", "null"], "description": "Customer ID if billable to customer"}}}}, "referenceNumber": {"type": ["string", "null"], "description": "Optional - Required by: QBO, Xero, QBD, NetSuite. Reference number"}, "externalId": {"type": ["string", "null"], "description": "External ID provided by the accounting platform"}, "platformUrl": {"type": ["string", "null"], "description": "Deep link URL to the platform"}, "accountId": {"type": ["string", "null"], "description": "Associated account ID"}, "classId": {"type": ["string", "null"], "description": "Associated class ID"}, "domain": {"type": "string", "description": "Platform domain identifier"}, "createdAtPlatform": {"type": "string", "format": "date-time", "description": "Creation timestamp from the platform"}, "updatedAtPlatform": {"type": ["string", "null"], "format": "date-time", "description": "Last update timestamp from the platform"}, "lastSyncedAt": {"type": "string", "format": "date-time", "description": "Last sync timestamp"}}, "examples": [{"id": "BILL001", "vendorId": "123", "externalBillId": "BILL001", "billNumber": "INV-2024-001", "billDate": "2024-01-15", "dueDate": "2024-02-14", "totalAmount": 1250.0, "balance": 1250.0, "currency": "USD", "status": "OPEN", "privateNote": "Office supplies for Q1", "exchangeRate": null, "lineItems": [{"description": "Office paper and supplies", "amount": 750.0, "accountId": "456", "classId": "789", "taxCodeId": null, "billableStatus": "NOT_BILLABLE", "customerId": null}, {"description": "Printer cartridges", "amount": 500.0, "accountId": "456", "classId": "789", "taxCodeId": null, "billableStatus": "NOT_BILLABLE", "customerId": null}], "referenceNumber": "PO-2024-001", "externalId": "BILL001", "platformUrl": "https://qbo.intuit.com/app/bill/BILL001", "accountId": "456", "classId": "789", "domain": "qbo", "createdAtPlatform": "2024-01-15T09:00:00Z", "updatedAtPlatform": "2024-01-15T09:00:00Z", "lastSyncedAt": "2024-01-15T10:30:00Z"}], "kafkaMessages": {"createRequestMessage": {"description": "Bill creation request (inbound to unified backend)", "topic": "entity-create-request", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "ZACT_APP", "destination": "UNIFIED_BACKEND", "messageType": "REQUEST", "erpSystem": "QBO", "entityOperation": {"entityType": "bill", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": [{"vendorId": "123", "billNumber": "INV-2024-001", "billDate": "2024-01-15", "dueDate": "2024-02-14", "totalAmount": 1250.0, "balance": 1250.0, "currency": "USD", "status": "OPEN", "lineItems": [{"description": "Office supplies", "amount": 1250.0, "accountId": "456"}]}], "status": null}}, "createResponseMessage": {"description": "Bill creation response (outbound from unified backend)", "topic": "entity-create-response", "example": {"messageId": "550e8400-e29b-41d4-a716-************", "correlationId": "550e8400-e29b-41d4-a716-************", "timestamp": *************, "source": "UNIFIED_BACKEND", "destination": "ZACT_APP", "messageType": "RESPONSE", "erpSystem": "QBO", "entityOperation": {"entityType": "bill", "operation": "CREATE"}, "filters": null, "metadata": null, "securityContext": {"connectionId": "conn-***********"}, "payload": {"data": {"id": "db-uuid-bill-001", "externalId": "BILL001", "vendorId": "123", "billNumber": "INV-2024-001", "billDate": "2024-01-15", "dueDate": "2024-02-14", "totalAmount": 1250.0, "balance": 1250.0, "currency": "USD", "status": "OPEN"}}, "status": {"code": "200", "message": "Bill created successfully"}}}}}