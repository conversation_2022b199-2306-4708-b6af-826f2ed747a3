import prisma from "../config/db";
import logger from "../utils/logger";
import { IUnifiedBill } from "../interfaces";

/**
 * Repository for Bill database operations
 */
export class BillRepository {
  /**
   * Save bill to database
   */
  static async save(
    unifiedBill: IUnifiedBill,
    connectionId: string
  ): Promise<any> {
    try {
      // Validate input
      if (!unifiedBill) {
        throw new Error("Unified bill data is null or undefined");
      }

      logger.info(
        `Processing unified bill data:`,
        JSON.stringify(unifiedBill, null, 2)
      );

      // Prepare data for database insertion
      const billData = {
        connectionId: connectionId,
        externalBillId: unifiedBill.id,
        vendorId: unifiedBill.vendorId,
        billNumber: unifiedBill.billNumber,
        billDate: new Date(unifiedBill.billDate),
        dueDate: new Date(unifiedBill.dueDate),
        totalAmount: unifiedBill.totalAmount,
        balance: unifiedBill.balance || 0,
        currency: unifiedBill.currency,
        status: (unifiedBill.status as any) || "OPEN",
        lineItems: unifiedBill.lineItems,
        domain: unifiedBill.domain,
        createdAtPlatform: unifiedBill.createdAtPlatform,
        updatedAtPlatform: unifiedBill.updatedAtPlatform,
      };

      // Save to database
      const savedEntity = await prisma.bill.create({
        data: billData,
      });

      logger.info(`Bill saved to database with ID: ${savedEntity.id}`);

      // Return saved entity in unified response format with camelCase
      return {
        id: savedEntity.id,
        externalBillId: savedEntity.externalBillId,
        vendorId: savedEntity.vendorId,
        billNumber: savedEntity.billNumber,
        billDate: savedEntity.billDate.toISOString(),
        dueDate: savedEntity.dueDate.toISOString(),
        totalAmount: savedEntity.totalAmount,
        balance: savedEntity.balance,
        currency: savedEntity.currency,
        status: savedEntity.status,
        lineItems: savedEntity.lineItems,
        domain: savedEntity.domain,
        createdAtPlatform: savedEntity.createdAtPlatform.toISOString(),
        updatedAtPlatform: savedEntity.updatedAtPlatform?.toISOString(),
        connectionId: savedEntity.connectionId,
        createdAt: savedEntity.lastSyncedAt.toISOString(),
      };
    } catch (error) {
      logger.error(`Error saving Bill to database:`, error);
      throw error;
    }
  }

  /**
   * Find bills by connection ID
   */
  static async findByConnectionId(connectionId: string) {
    return await prisma.bill.findMany({
      where: { connectionId },
    });
  }

  /**
   * Find bill by external ID
   */
  static async findByExternalId(externalId: string, connectionId: string) {
    return await prisma.bill.findFirst({
      where: {
        externalBillId: externalId,
        connectionId,
      },
    });
  }

  /**
   * Update bill
   */
  static async update(id: string, data: any) {
    return await prisma.bill.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete old bills
   */
  static async deleteOld(connectionId: string, days: number = 90) {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    return await prisma.bill.deleteMany({
      where: { connectionId, lastSyncedAt: { lt: cutoff } },
    });
  }
}
