# Environment Configuration Guide

## Overview

This document provides a comprehensive guide to all environment variables used in the Zact Unified Backend application. All environment variables are centrally managed through the `envConfig` object in `src/config/config.ts`.

## 🔧 Configuration Structure

The application uses a centralized configuration approach where all environment variables are:

1. Defined in `.env` file
2. Imported and structured in `src/config/config.ts`
3. Used throughout the application via `envConfig` object

## 📋 Environment Variables Reference

### Server Configuration

```bash
# Server port (default: 8080)
PORT=3000

# Node environment (development/production/test)
NODE_ENV=development
```

### Database Configuration

```bash
# PostgreSQL connection string
DATABASE_URL="postgresql://postgres:password@localhost:5432/zact_unified_db?schema=public"
```

### JWT Configuration

```bash
# JWT secret key for token signing
JWT_SECRET=your_jwt_secret_key_here_make_it_long_and_secure

# JWT token expiration time
JWT_EXPIRES_IN=1d
```

### Kafka Configuration

```bash
# Kafka client identifier
KAFKA_CLIENT_ID=zact-unified-backend

# Kafka broker addresses (comma-separated)
KAFKA_BROKERS=localhost:9092

# SSL configuration
KAFKA_SSL=false

# SASL authentication
KAFKA_SASL=false
KAFKA_SASL_MECHANISM=plain
KAFKA_SASL_USERNAME=
KAFKA_SASL_PASSWORD=

# Connection settings
KAFKA_CONNECTION_TIMEOUT=3000
KAFKA_REQUEST_TIMEOUT=30000

# Consumer group ID
KAFKA_CONSUMER_GROUP_ID=zact-unified-backend-group
```

### Logging Configuration

```bash
# Log level (error/warn/info/http/debug)
LOG_LEVEL=info

# Log file rotation settings
LOG_FILE_MAX_SIZE=10m
LOG_MAX_FILES=7
```

### QuickBooks Online (QBO) Configuration

```bash
# QBO application credentials
QBO_CLIENT_ID=your_qbo_client_id
QBO_CLIENT_SECRET=your_qbo_client_secret

# OAuth redirect URI
QBO_REDIRECT_URI=http://localhost:3000/auth/qbo/callback

# Environment (sandbox/production)
QBO_ENVIRONMENT=sandbox

# API URLs
QBO_API_BASE_URL=https://quickbooks.api.intuit.com
QBO_API_BASE_URL_SANDBOX=https://sandbox-quickbooks.api.intuit.com

# OAuth URLs
QBO_TOKEN_URL=https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
QBO_AUTH_URL=https://appcenter.intuit.com/connect/oauth2
QBO_REVOKE_URL=https://developer.api.intuit.com/v2/oauth2/tokens/revoke

# Optional: Pre-configured tokens (for testing)
QBO_ACCESS_TOKEN=
QBO_COMPANY_ID=
```

### API Gateway Configuration

```bash
# API Gateway base URL
API_GATEWAY_URL=http://localhost:8000

# API request timeout (milliseconds)
API_TIMEOUT=30000
```

### Sync Configuration

```bash
# Retry settings
MAX_SYNC_RETRIES=3
SYNC_RETRY_DELAY=1000

# Batch processing
SYNC_BATCH_SIZE=50
SYNC_MAX_BATCH_RETRIES=3

# Data cleanup
SYNC_DEFAULT_CLEANUP_DAYS=90

# Concurrency
MAX_CONCURRENT_SYNCS=3
```

### Kafka Message Configuration

```bash
# Message batching
KAFKA_MESSAGE_BATCH_SIZE=500
KAFKA_MESSAGE_BATCH_DELAY_MS=100
```

### Cron Job Configuration

```bash
# Incremental sync cron job
INCREMENTAL_SYNC_CRON_ENABLED=false
INCREMENTAL_SYNC_CRON_SCHEDULE=0 */6 * * *
INCREMENTAL_SYNC_CRON_BATCH_SIZE=1000
INCREMENTAL_SYNC_CRON_ENTITIES=ACCOUNT,VENDOR,CLASS
INCREMENTAL_SYNC_CRON_MAX_CONCURRENT=3
INCREMENTAL_SYNC_CRON_RETRY_ATTEMPTS=3
INCREMENTAL_SYNC_CRON_RETRY_DELAY=5000

# Timezone for cron execution
CRON_TIMEZONE=UTC
```

## 🚀 Usage Examples

### Accessing Configuration in Code

```typescript
import { envConfig } from "../config/config";

// Server configuration
const port = envConfig.server.port;
const nodeEnv = envConfig.server.nodeEnv;

// Database configuration
const dbUrl = envConfig.database.url;

// Kafka configuration
const kafkaBrokers = envConfig.kafka.brokers;
const kafkaClientId = envConfig.kafka.clientId;

// QBO configuration
const qboClientId = envConfig.qbo.clientId;
const qboEnvironment = envConfig.qbo.environment;

// Sync configuration
const maxRetries = envConfig.sync.maxRetries;
const batchSize = envConfig.sync.batchSize;
```

### Environment-Specific Configurations

#### Development Environment

```bash
NODE_ENV=development
LOG_LEVEL=debug
QBO_ENVIRONMENT=sandbox
INCREMENTAL_SYNC_CRON_ENABLED=false
```

#### Production Environment

```bash
NODE_ENV=production
LOG_LEVEL=info
QBO_ENVIRONMENT=production
INCREMENTAL_SYNC_CRON_ENABLED=true
```

## 🔒 Security Considerations

### Sensitive Variables

The following variables contain sensitive information and should be kept secure:

- `JWT_SECRET`
- `QBO_CLIENT_SECRET`
- `KAFKA_SASL_USERNAME`
- `KAFKA_SASL_PASSWORD`
- `DATABASE_URL`

### Best Practices

1. **Never commit `.env` files** to version control
2. **Use strong, unique secrets** for JWT and QBO credentials
3. **Rotate secrets regularly** in production
4. **Use environment-specific configurations** for different deployment stages
5. **Validate required environment variables** on application startup

## 🛠️ Setup Instructions

1. **Copy the example file:**

   ```bash
   cp .env.example .env
   ```

2. **Update the values** in `.env` with your specific configuration

3. **Verify configuration** by checking the application logs on startup

## 📚 Related Documentation

- [Kafka Configuration Guide](./KAFKA_SETUP.md)
- [QBO Integration Guide](./QBO_INTEGRATION.md)
- [Cron Jobs Documentation](./CRON_JOBS.md)
- [Database Setup Guide](./DATABASE_SETUP.md)
